-- Hardcoded Simple CSG Test
-- This script uses hardcoded values to ensure it works

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    print("=== STARTING HARDCODED TEST ===")

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.setLayer("PANEL")
    
    print("Creating PANEL rectangle from (0,0) to (300,400)")
    -- Create explicit rectangle for 300x400mm door
    G.rectangle({0, 0}, {300, 400})
    print("PANEL rectangle created")

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- Single hole in the center
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-10)  -- 10mm deep cut
    
    print("Creating circle at (150,200) with radius 30")
    -- Circle at exact center (150,200) with 30mm radius
    G.circle({150, 200}, 30)
    print("Circle created")

    print("=== HARDCODED TEST COMPLETED ===")
    print("- Door panel: 300x400x18mm")
    print("- Single hole at center (150,200) with 30mm radius")
    print("- Cut depth: 10mm with 20mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"
