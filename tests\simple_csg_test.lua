-- Ultra Simple CSG Test - Single hole in center
-- This script creates the simplest possible CSG operation

-- Material thickness
materialThickness = 18

-- Set door dimensions explicitly
X = 300  -- 300mm width
Y = 400  -- 400mm height

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    -- Create explicit rectangle for the door panel
    G.setLayer("PANEL")
    G.rectangle({0, 0}, {X, Y})  -- Explicit door rectangle using X,Y variables

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Single hole in the exact center of the door
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-10)  -- 10mm deep cut
    -- Create a single circle in the center
    local centerX = X / 2  -- 150mm (center of 300mm width)
    local centerY = Y / 2  -- 200mm (center of 400mm height)
    G.circle({centerX, centerY}, 30)  -- Circle at exact center with 30mm radius

    print("Ultra simple CSG test created:")
    print("- Door panel: " .. X .. "x" .. Y .. "x" .. materialThickness .. "mm")
    print("- Single hole at center (" .. centerX .. "," .. centerY .. ") with 30mm radius")
    print("- Cut depth: 10mm with 20mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"
