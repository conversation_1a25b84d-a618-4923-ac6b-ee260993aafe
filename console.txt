colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Dark (vibrant-dark)
colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Light (vibrant-light)
colorfulThemeService.ts:190 ✅ Registered theme: Neon Dark (neon-dark)
ocjsService.ts:74 OC.js Worker initialized
colorfulThemeService.ts:236 Colorful themes initialized: (3) ['vibrant-dark', 'vibrant-light', 'neon-dark']
App.vue:1043 Adek<PERSON>örü ba<PERSON>latıldı
App.vue:1057 Created welcome file: Untitled
EditorGroup.vue:102 Active file in group: group-1751742348974-oe0nya3if File: Untitled Content length: 48
Editor.vue:66 Initializing Monaco Editor with content: -- <PERSON><PERSON>
-- Kodu<PERSON><PERSON> buraya ekleyin

...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- <PERSON><PERSON><PERSON> dos<PERSON>ı
-- Kodu<PERSON>zu buraya ekleyin


Editor.vue:259 Container dimensions: {width: 1568, height: 839}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: [Array(1)]
Editor.vue:299 Line 2 tokens: [Array(1)]
Editor.vue:299 Line 3 tokens: [Array(0)]
Editor.vue:299 Line 4 tokens: [Array(0)]
useEditorState.ts:120 Creating new file: simple_csg_test.lua with content length: 1241
useEditorState.ts:121 Content preview: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with o...
EditorGroup.vue:102 Active file in group: group-1751742348974-oe0nya3if File: simple_csg_test.lua Content length: 1241
Editor.vue:66 Initializing Monaco Editor with content: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with o...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with one large cutting operation

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Large cutting operation that should be very visible
    G.setLayer("K_Freze100mm")  -- 100mm cylindrical tool (very large!)
    G.setThickness(-15)  -- Very deep cut - 15mm deep (almost through the 18mm door)
    -- Create a large rectangle in the center of the door
    G.rectangle({150, 200}, {350, 500})  -- Large rectangle that should be clearly visible

    print("Simple CSG test created:")
    print("- Door panel: 500x700x18mm")
    print("- Large rectangle cut: 150,200 to 350,500 (200x300mm)")
    print("- Cut depth: 15mm with 100mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

Editor.vue:259 Container dimensions: {width: 1568, height: 839}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: [Array(1)]
Editor.vue:299 Line 2 tokens: [Array(1)]
Editor.vue:299 Line 3 tokens: [Array(0)]
Editor.vue:299 Line 4 tokens: [Array(1)]
Editor.vue:299 Line 5 tokens: [Array(5)]
luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with one large cutting operation

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Large cutting operation that should be very visible
    G.setLayer("K_Freze100mm")  -- 100mm cylindrical tool (very large!)
    G.setThickness(-15)  -- Very deep cut - 15mm deep (almost through the 18mm door)
    -- Create a large rectangle in the center of the door
    G.rectangle({150, 200}, {350, 500})  -- Large rectangle that should be clearly visible

    print("Simple CSG test created:")
    print("- Door panel: 500x700x18mm")
    print("- Large rectangle cut: 150,200 to 350,500 (200x300mm)")
    print("- Cut depth: 15mm with 100mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
App.vue:690 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 0000012E5B3587C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B3587C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B3587C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B3587C0\nDEBUG: Engine is available, proceeding with engine calls\nSimple CSG test created:\n- Door panel: 500x700x18mm\n- Large rectangle cut: 150,200 to 350,500 (200x300mm)\n- Cut depth: 15mm with 100mm tool\nSwitch to 3D tab to see OpenCascade.js model\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 22:06:33\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"K_Freze100mm\": {\n      \"paths\": {\n        \"rect_bottom_9\": {\n          \"end\": [\n            190.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_12\": {\n          \"end\": [\n            190.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_11\": {\n          \"end\": [\n            390.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_10\": {\n          \"end\": [\n            390.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM3\": {\n      \"paths\": {\n        \"line_4\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            500.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"PANEL\": {\n      \"paths\": {\n        \"rect_bottom_5\": {\n          \"end\": [\n            40.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_8\": {\n          \"end\": [\n            40.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_7\": {\n          \"end\": [\n            540.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_6\": {\n          \"end\": [\n            540.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 22:06:33\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nDEBUG: modelMain function found in global scope\nDEBUG: Script execution time: 51ms\nDEBUG: Draw commands generated: 0",
  "error": null,
  "draw_commands": [],
  "execution_time_ms": 51,
  "debug_state": null,
  "makerjs_json": "{\n  \"models\": {\n    \"K_Freze100mm\": {\n      \"paths\": {\n        \"rect_bottom_9\": {\n          \"end\": [\n            190.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_12\": {\n          \"end\": [\n            190.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_11\": {\n          \"end\": [\n            390.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_10\": {\n          \"end\": [\n            390.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM3\": {\n      \"paths\": {\n        \"line_4\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            500.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"PANEL\": {\n      \"paths\": {\n        \"rect_bottom_5\": {\n          \"end\": [\n            40.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_8\": {\n          \"end\": [\n            40.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_7\": {\n          \"end\": [\n            540.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_6\": {\n          \"end\": [\n            540.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 22:06:33\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}",
  "makerjs_svg": null
}
App.vue:694 Draw commands received: []
App.vue:696 MakerJS JSON received: {
  "models": {
    "K_Freze100mm": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            190.0,
            596.0
          ],
          "origin": [
            390.0,
        ...
VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "K_Freze100mm": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            190.0,
            596.0
          ],
          "origin": [
            390.0,
        ...
VisualizationPanel.vue:1120 Converted makerjs JSON to 12 draw commands
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(0)}
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: undefined}
OCJSCanvas.vue:152 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
OCJSCanvas.vue:153 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:170 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(0)}
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
ocjsWorker.ts:803 Worker received message: createDoorBody
OCJSCanvas.vue:590 OCJSCanvas mounted
ocjsWorker.ts:73 OpenCascade.js initialized in worker
ocjsWorker.ts:89 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:93 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
ocjsWorker.ts:100 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
ocjsWorker.ts:105 ✅ Door body cached with ID: door_1751742412772
ocjsWorker.ts:845 Worker completed: createDoorBody
ocjsService.ts:232 ✅ Door body created successfully
ocjsService.ts:239 🔍 All tool operations: ['20mm End Mill (cylindrical, ⌀20mm) - 4 commands']
ocjsService.ts:242 🔧 Creating positioned tool shapes for 1 tool operations...
ocjsService.ts:249 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:803 Worker received message: createPositionedToolShapes
ocjsWorker.ts:218 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:283 🔧 Positioning line tool 0 at: X=0.0400m, Y=0.0050m, Z=0.2460m (from 290, 596 mm, door: 500x700 mm)
ocjsWorker.ts:283 🔧 Positioning line tool 1 at: X=-0.0600m, Y=0.0050m, Z=0.0960m (from 190, 446 mm, door: 500x700 mm)
ocjsWorker.ts:283 🔧 Positioning line tool 2 at: X=0.1400m, Y=0.0050m, Z=0.0960m (from 390, 446 mm, door: 500x700 mm)
ocjsWorker.ts:283 🔧 Positioning line tool 3 at: X=0.0400m, Y=0.0050m, Z=-0.0540m (from 290, 296 mm, door: 500x700 mm)
ocjsWorker.ts:313 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:845 Worker completed: createPositionedToolShapes
ocjsService.ts:261 ✅ Created 4 positioned shapes for 20mm End Mill
ocjsService.ts:272 🔧 Performing sweep operations with 4 positioned tool shapes...
ocjsWorker.ts:803 Worker received message: performSweepOperation
ocjsWorker.ts:478 🔧 Starting sweep operation: subtract
ocjsWorker.ts:497 🔧 Processing 4 tool geometries
ocjsWorker.ts:500 🔍 Starting CSG operations on door body
ocjsWorker.ts:522 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:532 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:522 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:532 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:522 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:532 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:522 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:532 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:561 ✅ Sweep operation completed, result cached with ID: result_1751742413134
ocjsWorker.ts:845 Worker completed: performSweepOperation
ocjsService.ts:279 ✅ Sweep operations completed: result_1751742413134
ocjsService.ts:282 🔧 Exporting final result to GLB...
ocjsWorker.ts:803 Worker received message: exportGLB
ocjsWorker.ts:578 🔧 Exporting to GLB...
ocjsWorker.ts:603 🔍 Exporting final shape to GLB format
ocjsWorker.ts:628 ✅ GLB export completed, size: 3232 bytes
ocjsWorker.ts:845 Worker completed: exportGLB
ocjsService.ts:284 ✅ Final GLB exported, size: 3232 bytes
OCJSCanvas.vue:179 ✅ Worker processing completed, GLB data size: 3232
OCJSCanvas.vue:193 Model URL created: blob:http://localhost:1420/648fdbf3-1819-4457-a84a-99bd8b2dfa65
OCJSCanvas.vue:231 ✅ Initializing Three.js scene
OCJSCanvas.vue:232 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:233 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
OCJSCanvas.vue:252 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:268 Renderer created with size: 1920 x 600
OCJSCanvas.vue:269 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:270 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:305 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:317 🔄 Loading GLB model: blob:http://localhost:1420/648fdbf3-1819-4457-a84a-99bd8b2dfa65
OCJSCanvas.vue:385 Loading progress: 100%
OCJSCanvas.vue:324 GLB model loaded successfully
OCJSCanvas.vue:348 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:360 Max dimension: 0.7
OCJSCanvas.vue:364 Camera distance: 2.0999999999999996
OCJSCanvas.vue:376 Model centered and camera adjusted
OCJSCanvas.vue:377 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
OCJSCanvas.vue:378 Model position: _Vector3 {x: 0, y: 0, z: 0}
