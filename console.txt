colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Dark (vibrant-dark)
colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Light (vibrant-light)
colorfulThemeService.ts:190 ✅ Registered theme: Neon Dark (neon-dark)
ocjsService.ts:74 OC.js Worker initialized
colorfulThemeService.ts:236 Colorful themes initialized: (3) ['vibrant-dark', 'vibrant-light', 'neon-dark']
App.vue:1043 Adeko Lua Editörü ba<PERSON>latıldı
App.vue:1057 Created welcome file: Untitled
EditorGroup.vue:102 Active file in group: group-1751742798457-64mid6yoa File: Untitled Content length: 48
Editor.vue:66 Initializing Monaco Editor with content: -- <PERSON><PERSON>
-- Kodu<PERSON><PERSON> buraya ekleyin

...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- <PERSON><PERSON><PERSON> dos<PERSON>ı
-- Kodu<PERSON>zu buraya ekleyin


Editor.vue:259 Container dimensions: {width: 1568, height: 839}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: [Array(1)]
Editor.vue:299 Line 2 tokens: [Array(1)]
Editor.vue:299 Line 3 tokens: [Array(0)]
Editor.vue:299 Line 4 tokens: [Array(0)]
useEditorState.ts:120 Creating new file: test-alias.lua with content length: 645
useEditorState.ts:121 Content preview: -- Test script to verify AdekoLib alias detection
G = ADekoLib

-- Test basic functions with alia...
EditorGroup.vue:102 Active file in group: group-1751742798457-64mid6yoa File: test-alias.lua Content length: 645
Editor.vue:66 Initializing Monaco Editor with content: -- Test script to verify AdekoLib alias detection
G = ADekoLib

-- Test basic functions with alia...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- Test script to verify AdekoLib alias detection
G = ADekoLib

-- Test basic functions with alias
G.start()
G.makePartShape("TestPart")

-- Test point creation
G.point(10, 20, 0)
G.point(30, 40, 0)
G.point(50, 60, 0)

-- Test shape operations
G.nextShape()
G.rectangle(0, 0, 100, 50)

-- Test analysis functions
local shapeCount = G.dataSize()
local pointCount = G.pointSize(1)

-- Test utility functions
G.showPoints(true)
G.enableListing(true)

-- Test with different alias
local Lib = ADekoLib
Lib.circle(25, 25, 15)

-- Test Menderes patterns
local pattern = G.menderes1({0, 0}, {100, 0}, 10, 5)

G.finish()

Editor.vue:259 Container dimensions: {width: 1568, height: 839}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
useEditorState.ts:120 Creating new file: simple_csg_test.lua with content length: 1241
useEditorState.ts:121 Content preview: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with o...
EditorGroup.vue:102 Active file in group: group-1751742798457-64mid6yoa File: simple_csg_test.lua Content length: 1241
Editor.vue:302  Token debug failed: Error: Model is disposed!
    at TextModel2._assertNotDisposed (textModel.js:230:19)
    at TextModel2.getLineCount (textModel.js:520:14)
    at Editor.vue:296:48
(anonymous) @ Editor.vue:302
setTimeout
(anonymous) @ Editor.vue:294
setTimeout
initializeEditor @ Editor.vue:268
(anonymous) @ Editor.vue:529
setTimeout
(anonymous) @ Editor.vue:528
(anonymous) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5475
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
noTracking @ reactivity.esm-bundler.js:914
push @ reactivity.esm-bundler.js:811
openFile @ useEditorState.ts:136
handleFileSelected @ App.vue:412
await in handleFileSelected
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
emit @ runtime-core.esm-bundler.js:6439
(anonymous) @ runtime-core.esm-bundler.js:8152
handleItemClick @ FileExplorer.vue:581
onClick @ FileExplorer.vue:74
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
Editor.vue:66 Initializing Monaco Editor with content: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with o...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with one large cutting operation

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Large cutting operation that should be very visible
    G.setLayer("K_Freze100mm")  -- 100mm cylindrical tool (very large!)
    G.setThickness(-15)  -- Very deep cut - 15mm deep (almost through the 18mm door)
    -- Create a large rectangle in the center of the door
    G.rectangle({150, 200}, {350, 500})  -- Large rectangle that should be clearly visible

    print("Simple CSG test created:")
    print("- Door panel: 500x700x18mm")
    print("- Large rectangle cut: 150,200 to 350,500 (200x300mm)")
    print("- Cut depth: 15mm with 100mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

Editor.vue:259 Container dimensions: {width: 1568, height: 839}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: [Array(1)]
Editor.vue:299 Line 2 tokens: [Array(1)]
Editor.vue:299 Line 3 tokens: [Array(0)]
Editor.vue:299 Line 4 tokens: [Array(1)]
Editor.vue:299 Line 5 tokens: [Array(5)]
luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with one large cutting operation

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Large cutting operation that should be very visible
    G.setLayer("K_Freze100mm")  -- 100mm cylindrical tool (very large!)
    G.setThickness(-15)  -- Very deep cut - 15mm deep (almost through the 18mm door)
    -- Create a large rectangle in the center of the door
    G.rectangle({150, 200}, {350, 500})  -- Large rectangle that should be clearly visible

    print("Simple CSG test created:")
    print("- Door panel: 500x700x18mm")
    print("- Large rectangle cut: 150,200 to 350,500 (200x300mm)")
    print("- Cut depth: 15mm with 100mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
App.vue:690 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 0000012E5E6205F0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5E6205F0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5E6205F0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5E6205F0\nDEBUG: Engine is available, proceeding with engine calls\nSimple CSG test created:\n- Door panel: 500x700x18mm\n- Large rectangle cut: 150,200 to 350,500 (200x300mm)\n- Cut depth: 15mm with 100mm tool\nSwitch to 3D tab to see OpenCascade.js model\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 22:14:05\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"K_Freze100mm\": {\n      \"paths\": {\n        \"rect_bottom_9\": {\n          \"end\": [\n            190.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_12\": {\n          \"end\": [\n            190.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_11\": {\n          \"end\": [\n            390.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_10\": {\n          \"end\": [\n            390.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM3\": {\n      \"paths\": {\n        \"line_4\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            500.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"PANEL\": {\n      \"paths\": {\n        \"rect_bottom_5\": {\n          \"end\": [\n            40.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_8\": {\n          \"end\": [\n            40.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_7\": {\n          \"end\": [\n            540.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_6\": {\n          \"end\": [\n            540.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 22:14:05\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nDEBUG: modelMain function found in global scope\nDEBUG: Script execution time: 118ms\nDEBUG: Draw commands generated: 0",
  "error": null,
  "draw_commands": [],
  "execution_time_ms": 118,
  "debug_state": null,
  "makerjs_json": "{\n  \"models\": {\n    \"K_Freze100mm\": {\n      \"paths\": {\n        \"rect_bottom_9\": {\n          \"end\": [\n            190.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_12\": {\n          \"end\": [\n            190.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_11\": {\n          \"end\": [\n            390.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_10\": {\n          \"end\": [\n            390.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM3\": {\n      \"paths\": {\n        \"line_4\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            500.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"PANEL\": {\n      \"paths\": {\n        \"rect_bottom_5\": {\n          \"end\": [\n            40.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_8\": {\n          \"end\": [\n            40.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_7\": {\n          \"end\": [\n            540.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_6\": {\n          \"end\": [\n            540.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 22:14:05\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}",
  "makerjs_svg": null
}
App.vue:694 Draw commands received: []
App.vue:696 MakerJS JSON received: {
  "models": {
    "K_Freze100mm": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            190.0,
            596.0
          ],
          "origin": [
            390.0,
        ...
VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "K_Freze100mm": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            190.0,
            596.0
          ],
          "origin": [
            390.0,
        ...
VisualizationPanel.vue:1120 Converted makerjs JSON to 12 draw commands
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(0)}
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:154 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:155 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:172 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(0)}
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
ocjsWorker.ts:806 Worker received message: createDoorBody
OCJSCanvas.vue:592 OCJSCanvas mounted
 OpenCascade.js initialized in worker
 Creating box with dimensions: 0.5 0.7 0.018
 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
 ✅ Door body cached with ID: door_1751742868827
 Worker completed: createDoorBody
 ✅ Door body created successfully
 🔍 All tool operations: ['20mm End Mill (cylindrical, ⌀20mm) - 4 commands']
 🔧 Creating positioned tool shapes for 1 tool operations...
 🔧 Processing 20mm End Mill with 4 commands
 Worker received message: createPositionedToolShapes
 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
 🔧 Positioning line tool 0 at: X=0.0000m, Y=0.0050m, Z=0.1500m (from 290, 596 mm, offset: 40, 96, door: 500x700 mm)
 🔧 Positioning line tool 1 at: X=-0.1000m, Y=0.0050m, Z=0.0000m (from 190, 446 mm, offset: 40, 96, door: 500x700 mm)
 🔧 Positioning line tool 2 at: X=0.1000m, Y=0.0050m, Z=0.0000m (from 390, 446 mm, offset: 40, 96, door: 500x700 mm)
 🔧 Positioning line tool 3 at: X=0.0000m, Y=0.0050m, Z=-0.1500m (from 290, 296 mm, offset: 40, 96, door: 500x700 mm)
 ✅ Created 4 positioned tool shapes
 Worker completed: createPositionedToolShapes
 ✅ Created 4 positioned shapes for 20mm End Mill
 🔧 Performing sweep operations with 4 positioned tool shapes...
 Worker received message: performSweepOperation
 🔧 Starting sweep operation: subtract
 🔧 Processing 4 tool geometries
 🔍 Starting CSG operations on door body
 🔧 Attempting to subtract tool 0...
 ✅ Tool 0 subtracted successfully
 🔧 Attempting to subtract tool 1...
 ✅ Tool 1 subtracted successfully
 🔧 Attempting to subtract tool 2...
 ✅ Tool 2 subtracted successfully
 🔧 Attempting to subtract tool 3...
 ✅ Tool 3 subtracted successfully
 ✅ Sweep operation completed, result cached with ID: result_1751742869868
 Worker completed: performSweepOperation
 ✅ Sweep operations completed: result_1751742869868
 🔧 Exporting final result to GLB...
 Worker received message: exportGLB
 🔧 Exporting to GLB...
 🔍 Exporting final shape to GLB format
 ✅ GLB export completed, size: 6660 bytes
 Worker completed: exportGLB
 ✅ Final GLB exported, size: 6660 bytes
 ✅ Worker processing completed, GLB data size: 6660
 Model URL created: blob:http://localhost:1420/9c8cea24-181d-4378-8ac9-433e8e8554d7
 ✅ Initializing Three.js scene
 📦 Container element: 
 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
 Renderer created with size: 1920 x 600
 Canvas element: 
 Canvas style: display: block; width: 1920px; height: 600px;
 ✅ Three.js scene initialized successfully
 🔄 Loading GLB model: blob:http://localhost:1420/9c8cea24-181d-4378-8ac9-433e8e8554d7
 Loading progress: 100%
 GLB model loaded successfully
 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
 Max dimension: 0.7
 Camera distance: 2.0999999999999996
 Model centered and camera adjusted
 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
 Model position: _Vector3 {x: 0, y: 0, z: 0}
 Wireframe mode: true
 Wireframe mode: false
 OCJSCanvas unmounted
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(0)}
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:154 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:155 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:172 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(0)}
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
ocjsWorker.ts:806 Worker received message: createDoorBody
ocjsWorker.ts:89 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:93 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
ocjsWorker.ts:100 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
ocjsWorker.ts:105 ✅ Door body cached with ID: door_1751742893685
ocjsWorker.ts:848 Worker completed: createDoorBody
OCJSCanvas.vue:592 OCJSCanvas mounted
ocjsService.ts:232 ✅ Door body created successfully
ocjsService.ts:239 🔍 All tool operations: ['20mm End Mill (cylindrical, ⌀20mm) - 4 commands']
ocjsService.ts:242 🔧 Creating positioned tool shapes for 1 tool operations...
ocjsService.ts:249 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:806 Worker received message: createPositionedToolShapes
ocjsWorker.ts:220 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:286 🔧 Positioning line tool 0 at: X=0.0000m, Y=0.0050m, Z=0.1500m (from 290, 596 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:286 🔧 Positioning line tool 1 at: X=-0.1000m, Y=0.0050m, Z=0.0000m (from 190, 446 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:286 🔧 Positioning line tool 2 at: X=0.1000m, Y=0.0050m, Z=0.0000m (from 390, 446 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:286 🔧 Positioning line tool 3 at: X=0.0000m, Y=0.0050m, Z=-0.1500m (from 290, 296 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:316 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:848 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 20mm End Mill
ocjsService.ts:274 🔧 Performing sweep operations with 4 positioned tool shapes...
ocjsWorker.ts:806 Worker received message: performSweepOperation
ocjsWorker.ts:481 🔧 Starting sweep operation: subtract
ocjsWorker.ts:500 🔧 Processing 4 tool geometries
ocjsWorker.ts:503 🔍 Starting CSG operations on door body
ocjsWorker.ts:525 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:535 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:535 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:535 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:535 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:564 ✅ Sweep operation completed, result cached with ID: result_1751742894139
ocjsWorker.ts:848 Worker completed: performSweepOperation
ocjsService.ts:281 ✅ Sweep operations completed: result_1751742894139
ocjsService.ts:284 🔧 Exporting final result to GLB...
ocjsWorker.ts:806 Worker received message: exportGLB
ocjsWorker.ts:581 🔧 Exporting to GLB...
ocjsWorker.ts:606 🔍 Exporting final shape to GLB format
ocjsWorker.ts:631 ✅ GLB export completed, size: 6660 bytes
ocjsWorker.ts:848 Worker completed: exportGLB
ocjsService.ts:286 ✅ Final GLB exported, size: 6660 bytes
OCJSCanvas.vue:181 ✅ Worker processing completed, GLB data size: 6660
OCJSCanvas.vue:195 Model URL created: blob:http://localhost:1420/fb884deb-74bc-492f-951c-708066faa652
OCJSCanvas.vue:598 🔧 Force initializing Three.js on mount
OCJSCanvas.vue:233 ✅ Initializing Three.js scene
OCJSCanvas.vue:234 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:235 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
OCJSCanvas.vue:254 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:270 Renderer created with size: 1920 x 600
OCJSCanvas.vue:271 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:272 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:307 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:601 ✅ Three.js initialized on mount
OCJSCanvas.vue:233 ✅ Initializing Three.js scene
OCJSCanvas.vue:234 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:235 📐 Container dimensions: {width: 1920, height: 600, offsetWidth: 1920, offsetHeight: 600}
OCJSCanvas.vue:254 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:270 Renderer created with size: 1920 x 600
OCJSCanvas.vue:271 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:272 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:307 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:319 🔄 Loading GLB model: blob:http://localhost:1420/fb884deb-74bc-492f-951c-708066faa652
OCJSCanvas.vue:387 Loading progress: 100%
OCJSCanvas.vue:326 GLB model loaded successfully
OCJSCanvas.vue:350 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:362 Max dimension: 0.7
OCJSCanvas.vue:366 Camera distance: 2.0999999999999996
OCJSCanvas.vue:378 Model centered and camera adjusted
OCJSCanvas.vue:379 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
OCJSCanvas.vue:380 Model position: _Vector3 {x: 0, y: 0, z: 0}
