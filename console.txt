 ✅ Registered theme: Vibrant Dark (vibrant-dark)
 ✅ Registered theme: Vibrant Light (vibrant-light)
 ✅ Registered theme: Neon Dark (neon-dark)
 OC.js Worker initialized
 Colorful themes initialized: Array(3)
 Adeko Lua Editörü b<PERSON>ıldı
 Created welcome file: Untitled
 Active file in group: group-1751741984836-ymnymv54o File: Untitled Content length: 48
 Initializing Monaco Editor with content: -- <PERSON><PERSON>
-- Kodu<PERSON>zu buraya ekleyin

...
 Monaco Editor created successfully
 Editor value: -- <PERSON><PERSON>
-- Kodunuzu buraya ekleyin


 Container dimensions: Object
 Applied colorful theme: Vibrant Dark
 ✅ Applied colorful theme: vibrant-dark
 🔄 Forced re-tokenization
 Editor layout forced
 Line 1 tokens: Array(1)
 Line 2 tokens: Array(1)
 Line 3 tokens: Array(1)
 Line 4 tokens: Array(1)
 Creating new file: simple_csg_test.lua with content length: 1241
 Content preview: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with o...
 Active file in group: group-1751741984836-ymnymv54o File: simple_csg_test.lua Content length: 1241
 Initializing Monaco Editor with content: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with o...
 Monaco Editor created successfully
 Editor value: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with one large cutting operation

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Large cutting operation that should be very visible
    G.setLayer("K_Freze100mm")  -- 100mm cylindrical tool (very large!)
    G.setThickness(-15)  -- Very deep cut - 15mm deep (almost through the 18mm door)
    -- Create a large rectangle in the center of the door
    G.rectangle({150, 200}, {350, 500})  -- Large rectangle that should be clearly visible

    print("Simple CSG test created:")
    print("- Door panel: 500x700x18mm")
    print("- Large rectangle cut: 150,200 to 350,500 (200x300mm)")
    print("- Cut depth: 15mm with 100mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

 Container dimensions: Object
 Applied colorful theme: Vibrant Dark
 ✅ Applied colorful theme: vibrant-dark
 🔄 Forced re-tokenization
 Editor layout forced
 Line 1 tokens: Array(1)
 Line 2 tokens: Array(1)
 Line 3 tokens: Array(1)
 Line 4 tokens: Array(1)
 Line 5 tokens: Array(1)
 === SENDING TO RUST ===
 Script content: -- Simple CSG Test - Minimal geometry for debugging
-- This script creates a very simple door with one large cutting operation

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Large cutting operation that should be very visible
    G.setLayer("K_Freze100mm")  -- 100mm cylindrical tool (very large!)
    G.setThickness(-15)  -- Very deep cut - 15mm deep (almost through the 18mm door)
    -- Create a large rectangle in the center of the door
    G.rectangle({150, 200}, {350, 500})  -- Large rectangle that should be clearly visible

    print("Simple CSG test created:")
    print("- Door panel: 500x700x18mm")
    print("- Large rectangle cut: 150,200 to 350,500 (200x300mm)")
    print("- Cut depth: 15mm with 100mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

 Lua library path: ./LIBRARY\luaLibrary
 Debug mode: false
 === END SENDING TO RUST ===
 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 0000012E5E572C60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5E572C60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5E572C60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5E572C60\nDEBUG: Engine is available, proceeding with engine calls\nSimple CSG test created:\n- Door panel: 500x700x18mm\n- Large rectangle cut: 150,200 to 350,500 (200x300mm)\n- Cut depth: 15mm with 100mm tool\nSwitch to 3D tab to see OpenCascade.js model\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 21:59:55\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"K_Freze100mm\": {\n      \"paths\": {\n        \"rect_bottom_9\": {\n          \"end\": [\n            190.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_12\": {\n          \"end\": [\n            190.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_11\": {\n          \"end\": [\n            390.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_10\": {\n          \"end\": [\n            390.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM3\": {\n      \"paths\": {\n        \"line_4\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            500.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"PANEL\": {\n      \"paths\": {\n        \"rect_bottom_5\": {\n          \"end\": [\n            40.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_8\": {\n          \"end\": [\n            40.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_7\": {\n          \"end\": [\n            540.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_6\": {\n          \"end\": [\n            540.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 21:59:55\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nDEBUG: modelMain function found in global scope\nDEBUG: Script execution time: 17ms\nDEBUG: Draw commands generated: 0",
  "error": null,
  "draw_commands": [],
  "execution_time_ms": 17,
  "debug_state": null,
  "makerjs_json": "{\n  \"models\": {\n    \"K_Freze100mm\": {\n      \"paths\": {\n        \"rect_bottom_9\": {\n          \"end\": [\n            190.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_12\": {\n          \"end\": [\n            190.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            596.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_11\": {\n          \"end\": [\n            390.0,\n            596.0\n          ],\n          \"origin\": [\n            390.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_10\": {\n          \"end\": [\n            390.0,\n            296.0\n          ],\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM3\": {\n      \"paths\": {\n        \"line_4\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            500.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"PANEL\": {\n      \"paths\": {\n        \"rect_bottom_5\": {\n          \"end\": [\n            40.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_8\": {\n          \"end\": [\n            40.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            796.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_7\": {\n          \"end\": [\n            540.0,\n            796.0\n          ],\n          \"origin\": [\n            540.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_6\": {\n          \"end\": [\n            540.0,\n            96.0\n          ],\n          \"origin\": [\n            40.0,\n            96.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 21:59:55\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}",
  "makerjs_svg": null
}
 Draw commands received: []
 MakerJS JSON received: {
  "models": {
    "K_Freze100mm": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            190.0,
            596.0
          ],
          "origin": [
            390.0,
        ...
 Processing makerjs JSON: {
  "models": {
    "K_Freze100mm": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            190.0,
            596.0
          ],
          "origin": [
            390.0,
        ...
 Converted makerjs JSON to 12 draw commands
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: Object
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: Object
OCJSCanvas.vue:152 Door parameters: Object
OCJSCanvas.vue:153 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:170 ⚙️ Sending to worker: Object
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: Object
ocjsWorker.ts:847 Worker received message: createDoorBody
OCJSCanvas.vue:590 OCJSCanvas mounted
 OpenCascade.js initialized in worker
 Creating box with dimensions: 0.5 0.7 0.018
 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
 ✅ Door body cached with ID: door_1751742022908
 Worker completed: createDoorBody
 ✅ Door body created successfully
 🔍 All tool operations: ['20mm End Mill (cylindrical, ⌀20mm) - 4 commands']
 🔧 Creating positioned tool shapes for 1 tool operations...
 🔧 Processing 20mm End Mill with 4 commands
 Worker received message: createPositionedToolShapes
 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
 🔧 Positioning line tool 0 at: X=0.0400m, Y=0.0050m, Z=0.2460m (from 290, 596 mm)
 🔧 Positioning line tool 1 at: X=-0.0600m, Y=0.0050m, Z=0.0960m (from 190, 446 mm)
 🔧 Positioning line tool 2 at: X=0.1400m, Y=0.0050m, Z=0.0960m (from 390, 446 mm)
 🔧 Positioning line tool 3 at: X=0.0400m, Y=0.0050m, Z=-0.0540m (from 290, 296 mm)
 ✅ Created 4 positioned tool shapes
 Worker completed: createPositionedToolShapes
 ✅ Created 4 positioned shapes for 20mm End Mill
 🔧 Performing sweep operations with 4 positioned tool shapes...
 Worker received message: performSweepOperation
 🔧 Starting sweep operation: subtract
 🔧 Processing 4 tool geometries
  Could not calculate initial volume: 
performSweepOperation @ ocjsWorker.ts?worker…ile&type=module:321
self.onmessage @ ocjsWorker.ts?worker…ile&type=module:654
 🔧 Attempting to subtract tool 0...
 ✅ Tool 0 subtracted successfully (could not calculate volume change)
 🔧 Attempting to subtract tool 1...
 ✅ Tool 1 subtracted successfully (could not calculate volume change)
 🔧 Attempting to subtract tool 2...
 ✅ Tool 2 subtracted successfully (could not calculate volume change)
 🔧 Attempting to subtract tool 3...
 ✅ Tool 3 subtracted successfully (could not calculate volume change)
 ✅ Sweep operation completed, result cached with ID: result_1751742023678
 Worker completed: performSweepOperation
 ✅ Sweep operations completed: result_1751742023678
 🔧 Exporting final result to GLB...
 Worker received message: exportGLB
 🔧 Exporting to GLB...
  Could not check shape validity: BindingError {name: 'BindingError', message: 'Tried to invoke ctor of BRepCheck_Analyzer with in…parameters (1) - expected (3) parameters instead!', stack: 'BindingError: Tried to invoke ctor of BRepCheck_An…ers/ocjsWorker.ts?worker_file&type=module:657:18)'}
exportToGLB @ ocjsWorker.ts?worker…ile&type=module:429
self.onmessage @ ocjsWorker.ts?worker…ile&type=module:657
  Could not calculate shape properties: 
exportToGLB @ ocjsWorker.ts?worker…ile&type=module:442
self.onmessage @ ocjsWorker.ts?worker…ile&type=module:657
 ✅ GLB export completed, size: 3232 bytes
 Worker completed: exportGLB
 ✅ Final GLB exported, size: 3232 bytes
 ✅ Worker processing completed, GLB data size: 3232
 Model URL created: blob:http://localhost:1420/842c4138-ee67-4600-bfc4-d8bbebccca14
 ✅ Initializing Three.js scene
 📦 Container element: 
 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
 Renderer created with size: 1920 x 600
 Canvas element: 
 Canvas style: display: block; width: 1920px; height: 600px;
 ✅ Three.js scene initialized successfully
 🔄 Loading GLB model: blob:http://localhost:1420/842c4138-ee67-4600-bfc4-d8bbebccca14
 Loading progress: 100%
 GLB model loaded successfully
 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
 Max dimension: 0.7
 Camera distance: 2.0999999999999996
 Model centered and camera adjusted
 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
 Model position: _Vector3 {x: 0, y: 0, z: 0}
 🚀 WORKER-BASED PROCESSING STARTED
 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(0), bottomTools: Array(0)}
 🔧 Using OCJS Service to extract door parameters
 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: undefined}
 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
 Door size in mm: W=500 H=700 T=18
 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(0), bottomTools: Array(0)}
 🔧 SWEEP OPERATION: Starting door processing with tools
 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
 Worker received message: createDoorBody
 Creating box with dimensions: 0.5 0.7 0.018
 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
 ✅ Door body cached with ID: door_1751742030712
 Worker completed: createDoorBody
 ✅ Door body created successfully
 🔍 All tool operations: []
  ⚠️ No tool operations found for processing
processDoorWithTools @ ocjsService.ts:212
await in processDoorWithTools
processDrawCommands @ OCJSCanvas.vue:70
watch.immediate @ OCJSCanvas.vue:345
callWithErrorHandling @ chunk-ZY5X6FX7.js:2270
callWithAsyncErrorHandling @ chunk-ZY5X6FX7.js:2277
baseWatchOptions.call @ chunk-ZY5X6FX7.js:8335
job @ chunk-ZY5X6FX7.js:2000
flushPreFlushCbs @ chunk-ZY5X6FX7.js:2426
updateComponentPreRender @ chunk-ZY5X6FX7.js:7582
componentUpdateFn @ chunk-ZY5X6FX7.js:7501
run @ chunk-ZY5X6FX7.js:488
updateComponent @ chunk-ZY5X6FX7.js:7376
processComponent @ chunk-ZY5X6FX7.js:7311
patch @ chunk-ZY5X6FX7.js:6816
patchBlockChildren @ chunk-ZY5X6FX7.js:7170
patchElement @ chunk-ZY5X6FX7.js:7088
processElement @ chunk-ZY5X6FX7.js:6947
patch @ chunk-ZY5X6FX7.js:6804
patchBlockChildren @ chunk-ZY5X6FX7.js:7170
patchElement @ chunk-ZY5X6FX7.js:7088
processElement @ chunk-ZY5X6FX7.js:6947
patch @ chunk-ZY5X6FX7.js:6804
componentUpdateFn @ chunk-ZY5X6FX7.js:7524
run @ chunk-ZY5X6FX7.js:488
runIfDirty @ chunk-ZY5X6FX7.js:526
callWithErrorHandling @ chunk-ZY5X6FX7.js:2270
flushJobs @ chunk-ZY5X6FX7.js:2478
Promise.then
queueFlush @ chunk-ZY5X6FX7.js:2392
queueJob @ chunk-ZY5X6FX7.js:2387
effect2.scheduler @ chunk-ZY5X6FX7.js:7566
trigger @ chunk-ZY5X6FX7.js:516
endBatch @ chunk-ZY5X6FX7.js:574
trigger @ chunk-ZY5X6FX7.js:961
set @ chunk-ZY5X6FX7.js:1432
toggleLayer @ VisualizationPanel.vue:535
callWithErrorHandling @ chunk-ZY5X6FX7.js:2270
callWithAsyncErrorHandling @ chunk-ZY5X6FX7.js:2277
emit @ chunk-ZY5X6FX7.js:8526
(anonymous) @ chunk-ZY5X6FX7.js:10235
toggleLayer @ LayerPanel.vue:18
onClick @ LayerPanel.vue:138
callWithErrorHandling @ chunk-ZY5X6FX7.js:2270
callWithAsyncErrorHandling @ chunk-ZY5X6FX7.js:2277
invoker @ chunk-ZY5X6FX7.js:11264
 ⚠️ No tools processed, returning simple door body GLB
 Worker received message: createSimpleBoxGLB
 🔧 Worker: Creating simple box GLB with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
ocjsWorker.ts:684 Creating simple box GLB: 0.5 x 0.7 x 0.018
ocjsWorker.ts:833 Created GLB file: 696 bytes
ocjsWorker.ts:868 ✅ Worker: Simple box GLB created, size: 696 bytes
ocjsWorker.ts:889 Worker completed: createSimpleBoxGLB
ocjsService.ts:297 ✅ Simple door GLB created, size: 696 bytes
OCJSCanvas.vue:179 ✅ Worker processing completed, GLB data size: 696
OCJSCanvas.vue:193 Model URL created: blob:http://localhost:1420/16f4ca0d-b433-4fb2-a441-c29f96885992
OCJSCanvas.vue:231 ✅ Initializing Three.js scene
OCJSCanvas.vue:232 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:233 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
OCJSCanvas.vue:252 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:268 Renderer created with size: 1920 x 600
OCJSCanvas.vue:269 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:270 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:305 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:317 🔄 Loading GLB model: blob:http://localhost:1420/16f4ca0d-b433-4fb2-a441-c29f96885992
OCJSCanvas.vue:385 Loading progress: 100%
OCJSCanvas.vue:324 GLB model loaded successfully
OCJSCanvas.vue:348 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:360 Max dimension: 0.7
OCJSCanvas.vue:364 Camera distance: 2.0999999999999996
OCJSCanvas.vue:376 Model centered and camera adjusted
OCJSCanvas.vue:377 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
OCJSCanvas.vue:378 Model position: _Vector3 {x: 0, y: 0, z: -0.009}
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(0)}
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: undefined}
OCJSCanvas.vue:152 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
OCJSCanvas.vue:153 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:170 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(0)}
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
ocjsWorker.ts:847 Worker received message: createDoorBody
ocjsWorker.ts:89 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:93 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
ocjsWorker.ts:100 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
ocjsWorker.ts:105 ✅ Door body cached with ID: door_1751742035773
ocjsWorker.ts:889 Worker completed: createDoorBody
ocjsService.ts:232 ✅ Door body created successfully
ocjsService.ts:239 🔍 All tool operations: ['20mm End Mill (cylindrical, ⌀20mm) - 4 commands']
ocjsService.ts:242 🔧 Creating positioned tool shapes for 1 tool operations...
ocjsService.ts:249 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:847 Worker received message: createPositionedToolShapes
ocjsWorker.ts:218 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:279 🔧 Positioning line tool 0 at: X=0.0400m, Y=0.0050m, Z=0.2460m (from 290, 596 mm)
ocjsWorker.ts:279 🔧 Positioning line tool 1 at: X=-0.0600m, Y=0.0050m, Z=0.0960m (from 190, 446 mm)
ocjsWorker.ts:279 🔧 Positioning line tool 2 at: X=0.1400m, Y=0.0050m, Z=0.0960m (from 390, 446 mm)
ocjsWorker.ts:279 🔧 Positioning line tool 3 at: X=0.0400m, Y=0.0050m, Z=-0.0540m (from 290, 296 mm)
ocjsWorker.ts:305 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:889 Worker completed: createPositionedToolShapes
ocjsService.ts:261 ✅ Created 4 positioned shapes for 20mm End Mill
ocjsService.ts:272 🔧 Performing sweep operations with 4 positioned tool shapes...
ocjsWorker.ts:847 Worker received message: performSweepOperation
ocjsWorker.ts:470 🔧 Starting sweep operation: subtract
ocjsWorker.ts:489 🔧 Processing 4 tool geometries
ocjsWorker.ts:498  Could not calculate initial volume: TypeError: oc.BRepGProp.VolumeProperties is not a function
    at performSweepOperation (ocjsWorker.ts:494:20)
    at self.onmessage (ocjsWorker.ts:860:18)
performSweepOperation @ ocjsWorker.ts:498
self.onmessage @ ocjsWorker.ts:860
ocjsWorker.ts:521 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:542 ✅ Tool 0 subtracted successfully (could not calculate volume change)
ocjsWorker.ts:521 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:542 ✅ Tool 1 subtracted successfully (could not calculate volume change)
ocjsWorker.ts:521 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:542 ✅ Tool 2 subtracted successfully (could not calculate volume change)
ocjsWorker.ts:521 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:542 ✅ Tool 3 subtracted successfully (could not calculate volume change)
ocjsWorker.ts:581 ✅ Sweep operation completed, result cached with ID: result_1751742036006
ocjsWorker.ts:889 Worker completed: performSweepOperation
ocjsService.ts:279 ✅ Sweep operations completed: result_1751742036006
ocjsService.ts:282 🔧 Exporting final result to GLB...
ocjsWorker.ts:847 Worker received message: exportGLB
ocjsWorker.ts:598 🔧 Exporting to GLB...
ocjsWorker.ts:629  Could not check shape validity: BindingError {name: 'BindingError', message: 'Tried to invoke ctor of BRepCheck_Analyzer with in…parameters (1) - expected (3) parameters instead!', stack: 'BindingError: Tried to invoke ctor of BRepCheck_An…ers/ocjsWorker.ts?worker_file&type=module:657:18)'}
exportToGLB @ ocjsWorker.ts:629
self.onmessage @ ocjsWorker.ts:863
ocjsWorker.ts:646  Could not calculate shape properties: TypeError: oc.BRepGProp.VolumeProperties is not a function
    at exportToGLB (ocjsWorker.ts:635:20)
    at self.onmessage (ocjsWorker.ts:863:18)
exportToGLB @ ocjsWorker.ts:646
self.onmessage @ ocjsWorker.ts:863
ocjsWorker.ts:672 ✅ GLB export completed, size: 3232 bytes
ocjsWorker.ts:889 Worker completed: exportGLB
ocjsService.ts:284 ✅ Final GLB exported, size: 3232 bytes
OCJSCanvas.vue:179 ✅ Worker processing completed, GLB data size: 3232
OCJSCanvas.vue:193 Model URL created: blob:http://localhost:1420/38795f01-22f8-4efb-a07c-e751cce298d0
OCJSCanvas.vue:231 ✅ Initializing Three.js scene
OCJSCanvas.vue:232 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:233 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
OCJSCanvas.vue:252 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:268 Renderer created with size: 1920 x 600
OCJSCanvas.vue:269 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:270 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:305 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:317 🔄 Loading GLB model: blob:http://localhost:1420/38795f01-22f8-4efb-a07c-e751cce298d0
OCJSCanvas.vue:385 Loading progress: 100%
OCJSCanvas.vue:324 GLB model loaded successfully
OCJSCanvas.vue:348 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:360 Max dimension: 0.7
OCJSCanvas.vue:364 Camera distance: 2.0999999999999996
OCJSCanvas.vue:376 Model centered and camera adjusted
OCJSCanvas.vue:377 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
OCJSCanvas.vue:378 Model position: _Vector3 {x: 0, y: 0, z: 0}
