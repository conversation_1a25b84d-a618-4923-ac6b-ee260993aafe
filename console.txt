VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
VisualizationPanel.vue:1120 Converted makerjs JSON to 79 draw commands
EditorGroup.vue:102 Active file in group: group-1751737350109-sn46k3zbj File: script.lua Content length: 2787
OCJSCanvas.vue:685 OCJSCanvas unmounted
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 301 x 845
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 301 x 845
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
Editor.vue:66 Initializing Monaco Editor with content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThick...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
Editor.vue:259 Container dimensions: {width: 1568, height: 539}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: [Array(1)]
Editor.vue:299 Line 2 tokens: [Array(1)]
Editor.vue:299 Line 3 tokens: [Array(0)]
Editor.vue:299 Line 4 tokens: [Array(1)]
Editor.vue:299 Line 5 tokens: [Array(4)]
luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
App.vue:690 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\n-13\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001FF7350F700\nDEBUG: Engine is available, proceeding with engine calls\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 21:13:51\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"H_Freze20mm_Ic_SF\": {\n      \"paths\": {\n        \"rect_bottom_41\": {\n          \"end\": [\n            676.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_44\": {\n          \"end\": [\n            676.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_43\": {\n          \"end\": [\n            1096.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_42\": {\n          \"end\": [\n            1096.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"H_Freze5mm_Ic\": {\n      \"paths\": {\n        \"polyline_seg_10\": {\n          \"end\": [\n            290.0,\n            238.174\n          ],\n          \"origin\": [\n            460.348,\n            153.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_11\": {\n          \"end\": [\n            119.652,\n            153.0\n          ],\n          \"origin\": [\n            290.0,\n            238.174\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_12\": {\n          \"end\": [\n            274.348,\n            246.0\n          ],\n          \"origin\": [\n            97.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_13\": {\n          \"end\": [\n            97.0,\n            334.674\n          ],\n          \"origin\": [\n            274.348,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_14\": {\n          \"end\": [\n            97.0,\n            157.326\n          ],\n          \"origin\": [\n            97.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_15\": {\n          \"end\": [\n            483.0,\n            157.326\n          ],\n          \"origin\": [\n            305.652,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_16\": {\n          \"end\": [\n            483.0,\n            334.674\n          ],\n          \"origin\": [\n            483.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_17\": {\n          \"end\": [\n            305.652,\n            246.0\n          ],\n          \"origin\": [\n            483.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_18\": {\n          \"end\": [\n            474.348,\n            346.0\n          ],\n          \"origin\": [\n            290.0,\n            253.826\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_19\": {\n          \"end\": [\n            290.0,\n            438.174\n          ],\n          \"origin\": [\n            474.348,\n            346.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_20\": {\n          \"end\": [\n            105.652,\n            346.0\n          ],\n          \
App.vue:694 Draw commands received: []
App.vue:696 MakerJS JSON received: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
VisualizationPanel.vue:1120 Converted makerjs JSON to 79 draw commands
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 🚀 WORKER-BASED PROCESSING STARTED
 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(2)}
 🔧 Using OCJS Service to extract door parameters
 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: undefined}
 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
 Door size in mm: W=500 H=700 T=18
 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(2)}
 🔧 SWEEP OPERATION: Starting door processing with tools
 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
ocjsWorker.ts:780 Worker received message: createDoorBody
ocjsWorker.ts:89 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:93 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
OCJSCanvas.vue:590 OCJSCanvas mounted
ocjsWorker.ts:100 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
ocjsWorker.ts:105 ✅ Door body cached with ID: door_1751739233178
ocjsWorker.ts:822 Worker completed: createDoorBody
ocjsService.ts:232 ✅ Door body created successfully
ocjsService.ts:239 🔍 All tool operations: (3) ['6mm End Mill (cylindrical, ⌀6mm) - 32 commands', '20mm End Mill (cylindrical, ⌀20mm) - 4 commands', '20mm End Mill (cylindrical, ⌀20mm) - 19 commands']
ocjsService.ts:242 🔧 Creating positioned tool shapes for 3 tool operations...
ocjsService.ts:249 🔧 Processing 6mm End Mill with 32 commands
ocjsWorker.ts:780 Worker received message: createPositionedToolShapes
ocjsWorker.ts:217 🔧 Creating 32 positioned cylindrical tool shapes for 6mm End Mill
ocjsWorker.ts:273 🔧 Positioning line tool 0 at: X=374.92, Y=0.00, Z=195.24
ocjsWorker.ts:273 🔧 Positioning line tool 1 at: X=204.58, Y=0.00, Z=195.24
ocjsWorker.ts:273 🔧 Positioning line tool 2 at: X=185.42, Y=0.00, Z=201.31
ocjsWorker.ts:273 🔧 Positioning line tool 3 at: X=185.42, Y=0.00, Z=289.99
ocjsWorker.ts:273 🔧 Positioning line tool 4 at: X=96.75, Y=0.00, Z=245.65
ocjsWorker.ts:273 🔧 Positioning line tool 5 at: X=394.08, Y=0.00, Z=201.31
ocjsWorker.ts:273 🔧 Positioning line tool 6 at: X=482.75, Y=0.00, Z=245.65
ocjsWorker.ts:273 🔧 Positioning line tool 7 at: X=394.08, Y=0.00, Z=289.99
ocjsWorker.ts:273 🔧 Positioning line tool 8 at: X=381.92, Y=0.00, Z=299.56
ocjsWorker.ts:273 🔧 Positioning line tool 9 at: X=381.92, Y=0.00, Z=391.74
ocjsWorker.ts:273 🔧 Positioning line tool 10 at: X=197.58, Y=0.00, Z=391.74
ocjsWorker.ts:273 🔧 Positioning line tool 11 at: X=197.58, Y=0.00, Z=299.56
ocjsWorker.ts:273 🔧 Positioning line tool 12 at: X=394.08, Y=0.00, Z=401.31
ocjsWorker.ts:273 🔧 Positioning line tool 13 at: X=482.75, Y=0.00, Z=445.65
ocjsWorker.ts:273 🔧 Positioning line tool 14 at: X=394.08, Y=0.00, Z=489.99
ocjsWorker.ts:273 🔧 Positioning line tool 15 at: X=204.58, Y=0.00, Z=696.06
ocjsWorker.ts:273 🔧 Positioning line tool 16 at: X=374.92, Y=0.00, Z=696.06
ocjsWorker.ts:273 🔧 Positioning line tool 17 at: X=289.75, Y=0.00, Z=738.65
ocjsWorker.ts:273 🔧 Positioning line tool 18 at: X=96.75, Y=0.00, Z=645.65
ocjsWorker.ts:273 🔧 Positioning line tool 19 at: X=185.42, Y=0.00, Z=601.31
ocjsWorker.ts:273 🔧 Positioning line tool 20 at: X=185.42, Y=0.00, Z=689.99
ocjsWorker.ts:273 🔧 Positioning line tool 21 at: X=394.08, Y=0.00, Z=601.31
ocjsWorker.ts:273 🔧 Positioning line tool 22 at: X=482.75, Y=0.00, Z=645.65
ocjsWorker.ts:273 🔧 Positioning line tool 23 at: X=394.08, Y=0.00, Z=689.99
ocjsWorker.ts:273 🔧 Positioning line tool 24 at: X=197.58, Y=0.00, Z=499.56
ocjsWorker.ts:273 🔧 Positioning line tool 25 at: X=381.92, Y=0.00, Z=499.56
ocjsWorker.ts:273 🔧 Positioning line tool 26 at: X=381.92, Y=0.00, Z=591.74
ocjsWorker.ts:273 🔧 Positioning line tool 27 at: X=197.58, Y=0.00, Z=591.74
ocjsWorker.ts:273 🔧 Positioning line tool 28 at: X=185.42, Y=0.00, Z=489.99
ocjsWorker.ts:273 🔧 Positioning line tool 29 at: X=96.75, Y=0.00, Z=445.65
ocjsWorker.ts:273 🔧 Positioning line tool 30 at: X=185.42, Y=0.00, Z=401.31
ocjsWorker.ts:273 🔧 Positioning line tool 31 at: X=289.75, Y=0.00, Z=152.65
ocjsWorker.ts:295 ✅ Created 32 positioned tool shapes
ocjsWorker.ts:822 Worker completed: createPositionedToolShapes
ocjsService.ts:261 ✅ Created 32 positioned shapes for 6mm End Mill
ocjsService.ts:249 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:780 Worker received message: createPositionedToolShapes
ocjsWorker.ts:217 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:273 🔧 Positioning line tool 0 at: X=885.75, Y=-0.01, Z=755.65
ocjsWorker.ts:273 🔧 Positioning line tool 1 at: X=675.75, Y=-0.01, Z=445.65
ocjsWorker.ts:273 🔧 Positioning line tool 2 at: X=1095.75, Y=-0.01, Z=445.65
ocjsWorker.ts:273 🔧 Positioning line tool 3 at: X=885.75, Y=-0.01, Z=135.65
ocjsWorker.ts:295 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:822 Worker completed: createPositionedToolShapes
ocjsService.ts:261 ✅ Created 4 positioned shapes for 20mm End Mill
ocjsService.ts:249 🔧 Processing 20mm End Mill with 19 commands
ocjsWorker.ts:780 Worker received message: createPositionedToolShapes
ocjsWorker.ts:217 🔧 Creating 19 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:273 🔧 Positioning line tool 0 at: X=885.75, Y=-0.01, Z=245.65
ocjsWorker.ts:273 🔧 Positioning line tool 1 at: X=885.75, Y=-0.01, Z=245.65
ocjsWorker.ts:273 🔧 Positioning line tool 2 at: X=885.75, Y=-0.01, Z=445.65
ocjsWorker.ts:273 🔧 Positioning line tool 3 at: X=885.75, Y=-0.01, Z=445.65
ocjsWorker.ts:273 🔧 Positioning line tool 4 at: X=885.75, Y=-0.01, Z=645.65
ocjsWorker.ts:273 🔧 Positioning line tool 5 at: X=885.75, Y=-0.01, Z=645.65
ocjsWorker.ts:273 🔧 Positioning line tool 6 at: X=4.58, Y=-0.01, Z=148.36
ocjsWorker.ts:273 🔧 Positioning line tool 7 at: X=251.04, Y=-0.01, Z=647.48
ocjsWorker.ts:273 🔧 Positioning line tool 8 at: X=4.58, Y=-0.01, Z=98.36
ocjsWorker.ts:273 🔧 Positioning line tool 9 at: X=51.92, Y=-0.01, Z=150.94
ocjsWorker.ts:273 🔧 Positioning line tool 10 at: X=248.46, Y=-0.01, Z=694.82
ocjsWorker.ts:273 🔧 Positioning line tool 11 at: X=34.92, Y=-0.01, Z=100.94
ocjsWorker.ts:273 🔧 Positioning line tool 12 at: X=246.01, Y=-0.01, Z=160.79
ocjsWorker.ts:273 🔧 Positioning line tool 13 at: X=269.14, Y=-0.01, Z=146.33
ocjsWorker.ts:273 🔧 Positioning line tool 14 at: X=240.81, Y=-0.01, Z=161.95
ocjsWorker.ts:273 🔧 Positioning line tool 15 at: X=274.35, Y=-0.01, Z=145.18
ocjsWorker.ts:273 🔧 Positioning line tool 16 at: X=28.25, Y=-0.01, Z=149.65
ocjsWorker.ts:273 🔧 Positioning line tool 17 at: X=249.75, Y=-0.01, Z=671.15
ocjsWorker.ts:273 🔧 Positioning line tool 18 at: X=19.75, Y=-0.01, Z=99.65
ocjsWorker.ts:295 ✅ Created 19 positioned tool shapes
ocjsWorker.ts:822 Worker completed: createPositionedToolShapes
ocjsService.ts:261 ✅ Created 19 positioned shapes for 20mm End Mill
ocjsService.ts:272 🔧 Performing sweep operations with 55 positioned tool shapes...
ocjsWorker.ts:780 Worker received message: performSweepOperation
ocjsWorker.ts:460 🔧 Starting sweep operation: subtract
ocjsWorker.ts:479 🔧 Processing 55 tool geometries
ocjsWorker.ts:501 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:510 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:510 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:510 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:510 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 4...
ocjsWorker.ts:510 ✅ Tool 4 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 5...
ocjsWorker.ts:510 ✅ Tool 5 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 6...
ocjsWorker.ts:510 ✅ Tool 6 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 7...
ocjsWorker.ts:510 ✅ Tool 7 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 8...
ocjsWorker.ts:510 ✅ Tool 8 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 9...
ocjsWorker.ts:510 ✅ Tool 9 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 10...
ocjsWorker.ts:510 ✅ Tool 10 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 11...
ocjsWorker.ts:510 ✅ Tool 11 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 12...
ocjsWorker.ts:510 ✅ Tool 12 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 13...
ocjsWorker.ts:510 ✅ Tool 13 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 14...
ocjsWorker.ts:510 ✅ Tool 14 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 15...
ocjsWorker.ts:510 ✅ Tool 15 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 16...
ocjsWorker.ts:510 ✅ Tool 16 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 17...
ocjsWorker.ts:510 ✅ Tool 17 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 18...
ocjsWorker.ts:510 ✅ Tool 18 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 19...
ocjsWorker.ts:510 ✅ Tool 19 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 20...
ocjsWorker.ts:510 ✅ Tool 20 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 21...
ocjsWorker.ts:510 ✅ Tool 21 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 22...
ocjsWorker.ts:510 ✅ Tool 22 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 23...
ocjsWorker.ts:510 ✅ Tool 23 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 24...
ocjsWorker.ts:510 ✅ Tool 24 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 25...
ocjsWorker.ts:510 ✅ Tool 25 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 26...
ocjsWorker.ts:510 ✅ Tool 26 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 27...
ocjsWorker.ts:510 ✅ Tool 27 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 28...
ocjsWorker.ts:510 ✅ Tool 28 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 29...
ocjsWorker.ts:510 ✅ Tool 29 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 30...
ocjsWorker.ts:510 ✅ Tool 30 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 31...
ocjsWorker.ts:510 ✅ Tool 31 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 32...
ocjsWorker.ts:510 ✅ Tool 32 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 33...
ocjsWorker.ts:510 ✅ Tool 33 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 34...
ocjsWorker.ts:510 ✅ Tool 34 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 35...
ocjsWorker.ts:510 ✅ Tool 35 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 36...
ocjsWorker.ts:510 ✅ Tool 36 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 37...
ocjsWorker.ts:510 ✅ Tool 37 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 38...
ocjsWorker.ts:510 ✅ Tool 38 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 39...
ocjsWorker.ts:510 ✅ Tool 39 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 40...
ocjsWorker.ts:510 ✅ Tool 40 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 41...
ocjsWorker.ts:510 ✅ Tool 41 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 42...
ocjsWorker.ts:510 ✅ Tool 42 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 43...
ocjsWorker.ts:510 ✅ Tool 43 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 44...
ocjsWorker.ts:510 ✅ Tool 44 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 45...
ocjsWorker.ts:510 ✅ Tool 45 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 46...
ocjsWorker.ts:510 ✅ Tool 46 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 47...
ocjsWorker.ts:510 ✅ Tool 47 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 48...
ocjsWorker.ts:510 ✅ Tool 48 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 49...
ocjsWorker.ts:510 ✅ Tool 49 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 50...
ocjsWorker.ts:510 ✅ Tool 50 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 51...
ocjsWorker.ts:510 ✅ Tool 51 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 52...
ocjsWorker.ts:510 ✅ Tool 52 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 53...
ocjsWorker.ts:510 ✅ Tool 53 subtracted successfully
ocjsWorker.ts:501 🔧 Attempting to subtract tool 54...
ocjsWorker.ts:510 ✅ Tool 54 subtracted successfully
ocjsWorker.ts:540 ✅ Sweep operation completed, result cached with ID: result_1751739234340
ocjsWorker.ts:822 Worker completed: performSweepOperation
ocjsService.ts:279 ✅ Sweep operations completed: result_1751739234340
ocjsService.ts:282 🔧 Exporting final result to GLB...
ocjsWorker.ts:780 Worker received message: exportGLB
ocjsWorker.ts:557 🔧 Exporting to GLB...
ocjsWorker.ts:605 ✅ GLB export completed, size: 3232 bytes
ocjsWorker.ts:822 Worker completed: exportGLB
ocjsService.ts:284 ✅ Final GLB exported, size: 3232 bytes
OCJSCanvas.vue:179 ✅ Worker processing completed, GLB data size: 3232
OCJSCanvas.vue:193 Model URL created: blob:http://localhost:1420/d7ce6235-e599-48ff-a15f-318172f6156a
OCJSCanvas.vue:231 ✅ Initializing Three.js scene
OCJSCanvas.vue:232 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:233 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
OCJSCanvas.vue:252 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:268 Renderer created with size: 1920 x 600
OCJSCanvas.vue:269 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:270 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:305 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:317 🔄 Loading GLB model: blob:http://localhost:1420/d7ce6235-e599-48ff-a15f-318172f6156a
OCJSCanvas.vue:385 Loading progress: 100%
OCJSCanvas.vue:324 GLB model loaded successfully
OCJSCanvas.vue:348 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:360 Max dimension: 0.7
OCJSCanvas.vue:364 Camera distance: 2.0999999999999996
OCJSCanvas.vue:376 Model centered and camera adjusted
OCJSCanvas.vue:377 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
OCJSCanvas.vue:378 Model position: _Vector3 {x: 0, y: 0, z: 0}
