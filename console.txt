VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "20MM": {
      "paths": {
        "polyline_arc_10": {
          "clockwise": true,
          "endAngle": 90.0,
          "origin": [
            290.0,
            446.0
        ...
VisualizationPanel.vue:355 Converting arc path: {clockwise: true, endAngle: 90, origin: Array(2), radius: 30, startAngle: 180, …}
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 180
VisualizationPanel.vue:359 path.endAngle: 90
VisualizationPanel.vue:365 Final startAngle: 180
VisualizationPanel.vue:366 Final endAngle: 90
VisualizationPanel.vue:355 Converting arc path: {clockwise: true, endAngle: 180, origin: Array(2), radius: 30, startAngle: 270, …}
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 270
VisualizationPanel.vue:359 path.endAngle: 180
VisualizationPanel.vue:365 Final startAngle: 270
VisualizationPanel.vue:366 Final endAngle: 180
VisualizationPanel.vue:355 Converting arc path: {clockwise: true, endAngle: -90, origin: Array(2), radius: 30, startAngle: 0, …}
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 0
VisualizationPanel.vue:359 path.endAngle: -90
VisualizationPanel.vue:365 Final startAngle: 0
VisualizationPanel.vue:366 Final endAngle: -90
VisualizationPanel.vue:355 Converting arc path: {clockwise: true, endAngle: 0, origin: Array(2), radius: 30, startAngle: 90, …}
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 90
VisualizationPanel.vue:359 path.endAngle: 0
VisualizationPanel.vue:365 Final startAngle: 90
VisualizationPanel.vue:366 Final endAngle: 0
VisualizationPanel.vue:1120 Converted makerjs JSON to 12 draw commands
EditorGroup.vue:102 Active file in group: group-1751743522386-3am56v36j File: simple_csg_test.lua Content length: 1487
OCJSCanvas.vue:687 OCJSCanvas unmounted
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 301 x 628
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 301 x 628
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
Editor.vue:66 Initializing Monaco Editor with content: -- Ultra Simple CSG Test - Single hole in center
-- This script creates the simplest possible CSG op...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- Ultra Simple CSG Test - Single hole in center
-- This script creates the simplest possible CSG operation

-- Material thickness
materialThickness = 18

-- Set door dimensions explicitly
X = 300  -- 300mm width
Y = 400  -- 400mm height

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    -- Create explicit rectangle for the door panel
    G.setLayer("PANEL")
    G.rectangle({0, 0}, {X, Y})  -- Explicit door rectangle using X,Y variables

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Single hole in the exact center of the door
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-10)  -- 10mm deep cut
    -- Create a single circle in the center
    local centerX = X / 2  -- 150mm (center of 300mm width)
    local centerY = Y / 2  -- 200mm (center of 400mm height)
    G.circle({centerX, centerY}, 30)  -- Circle at exact center with 30mm radius

    print("Ultra simple CSG test created:")
    print("- Door panel: " .. X .. "x" .. Y .. "x" .. materialThickness .. "mm")
    print("- Single hole at center (" .. centerX .. "," .. centerY .. ") with 30mm radius")
    print("- Cut depth: 10mm with 20mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

Editor.vue:259 Container dimensions: {width: 848, height: 322}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: [Array(1)]
Editor.vue:299 Line 2 tokens: [Array(1)]
Editor.vue:299 Line 3 tokens: [Array(0)]
Editor.vue:299 Line 4 tokens: [Array(1)]
Editor.vue:299 Line 5 tokens: [Array(5)]
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 0 x 0
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
useEditorState.ts:120 Creating new file: hardcoded_test.lua with content length: 1328
useEditorState.ts:121 Content preview: -- Hardcoded Simple CSG Test
-- This script uses hardcoded values to ensure it works

-- Material th...
EditorGroup.vue:102 Active file in group: group-1751743522386-3am56v36j File: hardcoded_test.lua Content length: 1328
Editor.vue:66 Initializing Monaco Editor with content: -- Hardcoded Simple CSG Test
-- This script uses hardcoded values to ensure it works

-- Material th...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- Hardcoded Simple CSG Test
-- This script uses hardcoded values to ensure it works

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    print("=== STARTING HARDCODED TEST ===")

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.setLayer("PANEL")
    
    print("Creating PANEL rectangle from (0,0) to (300,400)")
    -- Create explicit rectangle for 300x400mm door
    G.rectangle({0, 0}, {300, 400})
    print("PANEL rectangle created")

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- Single hole in the center
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-10)  -- 10mm deep cut
    
    print("Creating circle at (150,200) with radius 30")
    -- Circle at exact center (150,200) with 30mm radius
    G.circle({150, 200}, 30)
    print("Circle created")

    print("=== HARDCODED TEST COMPLETED ===")
    print("- Door panel: 300x400x18mm")
    print("- Single hole at center (150,200) with 30mm radius")
    print("- Cut depth: 10mm with 20mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

Editor.vue:259 Container dimensions: {width: 848, height: 322}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: [Array(1)]
Editor.vue:299 Line 2 tokens: [Array(1)]
Editor.vue:299 Line 3 tokens: [Array(0)]
Editor.vue:299 Line 4 tokens: [Array(1)]
Editor.vue:299 Line 5 tokens: [Array(5)]
luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- Hardcoded Simple CSG Test
-- This script uses hardcoded values to ensure it works

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    print("=== STARTING HARDCODED TEST ===")

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.setLayer("PANEL")
    
    print("Creating PANEL rectangle from (0,0) to (300,400)")
    -- Create explicit rectangle for 300x400mm door
    G.rectangle({0, 0}, {300, 400})
    print("PANEL rectangle created")

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- Single hole in the center
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-10)  -- 10mm deep cut
    
    print("Creating circle at (150,200) with radius 30")
    -- Circle at exact center (150,200) with 30mm radius
    G.circle({150, 200}, 30)
    print("Circle created")

    print("=== HARDCODED TEST COMPLETED ===")
    print("- Door panel: 300x400x18mm")
    print("- Single hole at center (150,200) with 30mm radius")
    print("- Cut depth: 10mm with 20mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
App.vue:690 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 000002A08B97AE50\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000002A08B97AE50\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000002A08B97AE50\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000002A08B97AE50\nDEBUG: Engine is available, proceeding with engine calls\n=== STARTING HARDCODED TEST ===\nCreating PANEL rectangle from (0,0) to (300,400)\nPANEL rectangle created\nCreating circle at (150,200) with radius 30\nDEBUG safe_engine_call: Calling arc with parameters:\n  name=polyline_arc_9\n  cx=190.0\n  cy=296.0\n  radius=30.0\n  start_angle=0.0\n  end_angle=90.0\n  clockwise=false\nDEBUG: Original arc 'polyline_arc_9' with start_angle=0.0 end_angle=90.0 clockwise=false face=top\nDEBUG: Using complementary arc logic\nDEBUG: Final arc 'polyline_arc_9' with start_angle=90.0 end_angle=0.0 clockwise=true\nDEBUG: Stored arc has start_angle=90.0 end_angle=0.0 clockwise=true\nDEBUG safe_engine_call: Calling arc with parameters:\n  name=polyline_arc_10\n  cx=190.0\n  cy=296.0\n  radius=30.0\n  start_angle=90.0\n  end_angle=180.0\n  clockwise=false\nDEBUG: Original arc 'polyline_arc_10' with start_angle=90.0 end_angle=180.0 clockwise=false face=top\nDEBUG: Using complementary arc logic\nDEBUG: Final arc 'polyline_arc_10' with start_angle=180.0 end_angle=90.0 clockwise=true\nDEBUG: Stored arc has start_angle=180.0 end_angle=90.0 clockwise=true\nDEBUG safe_engine_call: Calling arc with parameters:\n  name=polyline_arc_11\n  cx=190.0\n  cy=296.0\n  radius=30.0\n  start_angle=180.0\n  end_angle=270.0\n  clockwise=false\nDEBUG: Original arc 'polyline_arc_11' with start_angle=180.0 end_angle=270.0 clockwise=false face=top\nDEBUG: Using complementary arc logic\nDEBUG: Final arc 'polyline_arc_11' with start_angle=270.0 end_angle=180.0 clockwise=true\nDEBUG: Stored arc has start_angle=270.0 end_angle=180.0 clockwise=true\nDEBUG safe_engine_call: Calling arc with parameters:\n  name=polyline_arc_12\n  cx=190.0\n  cy=296.0\n  radius=30.0\n  start_angle=270.0\n  end_angle=0.0\n  clockwise=false\nDEBUG: Original arc 'polyline_arc_12' with start_angle=270.0 end_angle=0.0 clockwise=false face=top\nDEBUG: Using complementary arc logic\nDEBUG: Final arc 'polyline_arc_12' with start_angle=0.0 end_angle=-90.0 clockwise=true\nDEBUG: Stored arc has start_angle=0.0 end_angle=-90.0 clockwise=true\nCircle created\n=== HARDCODED TEST COMPLETED ===\n- Door panel: 300x400x18mm\n- Single hole at center (150,200) with 30mm radius\n- Cut depth: 10mm with 20mm tool\nSwitch to 3D tab to see OpenCascade.js model\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 22:33:37\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"20MM\": {\n      \"paths\": {\n        \"polyline_arc_10\": {\n          \"clockwise\": true,\n          \"endAngle\": 90.0,\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"radius\": 30.0,\n          \"startAngle\": 180.0,\n          \"type\": \"arc\"\n        },\n        \"polyline_arc_11\": {\n          \"clockwise\": true,\n          \"endAngle\": 180.0,\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"radius\": 30.0,\n          \"startAngle\": 270.0,\n          \"type\": \"arc\"\n        },\n        \"polyline_arc_12\": {\n          \"clockwise\": true,\n          \"endAngle\": -90.0,\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"radius\": 30.0,\n          \"startAngle\": 0.0,\n          \"type\": \"arc\"\n        },\n        \"polyline_arc_9\": {\n          \"clockwise\": true,\n          \"endAngle\": 0.0,\n          \"origin\": [\n            190.0,\n            296.0\n          ],\n          \"radius\": 30.0,\n          \"startAngle\": 90.0,\n          \"type\": \"arc\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\": 
App.vue:694 Draw commands received: []
App.vue:696 MakerJS JSON received: {
  "models": {
    "20MM": {
      "paths": {
        "polyline_arc_10": {
          "clockwise": true,
          "endAngle": 90.0,
          "origin": [
            190.0,
            296.0
        ...
VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "20MM": {
      "paths": {
        "polyline_arc_10": {
          "clockwise": true,
          "endAngle": 90.0,
          "origin": [
            190.0,
            296.0
        ...
VisualizationPanel.vue:355 Converting arc path: {clockwise: true, endAngle: 90, origin: Array(2), radius: 30, startAngle: 180, …}
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 180
VisualizationPanel.vue:359 path.endAngle: 90
VisualizationPanel.vue:365 Final startAngle: 180
VisualizationPanel.vue:366 Final endAngle: 90
VisualizationPanel.vue:355 Converting arc path: {clockwise: true, endAngle: 180, origin: Array(2), radius: 30, startAngle: 270, …}
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 270
VisualizationPanel.vue:359 path.endAngle: 180
VisualizationPanel.vue:365 Final startAngle: 270
VisualizationPanel.vue:366 Final endAngle: 180
VisualizationPanel.vue:355 Converting arc path: {clockwise: true, endAngle: -90, origin: Array(2), radius: 30, startAngle: 0, …}
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 0
VisualizationPanel.vue:359 path.endAngle: -90
VisualizationPanel.vue:365 Final startAngle: 0
VisualizationPanel.vue:366 Final endAngle: -90
VisualizationPanel.vue:355 Converting arc path: {clockwise: true, endAngle: 0, origin: Array(2), radius: 30, startAngle: 90, …}
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 90
VisualizationPanel.vue:359 path.endAngle: 0
VisualizationPanel.vue:365 Final startAngle: 90
VisualizationPanel.vue:366 Final endAngle: 0
VisualizationPanel.vue:1120 Converted makerjs JSON to 12 draw commands
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1200 x 660
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1200 x 660
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(0)}
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: {width: 300, height: 400, thickness: 18, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:154 Door parameters: {width: 0.3, height: 0.4, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:155 Door size in mm: W=300 H=400 T=18
OCJSCanvas.vue:172 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(0)}
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: {width: 0.3, height: 0.4, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
ocjsWorker.ts:806 Worker received message: createDoorBody
ocjsWorker.ts:89 Creating box with dimensions: 0.3 0.4 0.018
ocjsWorker.ts:93 🚪 Door body dimensions: W=0.3m, H=0.4m, T=0.018m
ocjsWorker.ts:100 🚪 Door body centered at origin: X=[-0.15, 0.15], Y=[-0.2, 0.2], Z=[-0.009, 0.009]
ocjsWorker.ts:105 ✅ Door body cached with ID: door_1751744021081
OCJSCanvas.vue:592 OCJSCanvas mounted
ocjsWorker.ts:848 Worker completed: createDoorBody
ocjsService.ts:232 ✅ Door body created successfully
ocjsService.ts:239 🔍 All tool operations: ['20mm End Mill (cylindrical, ⌀20mm) - 4 commands']
ocjsService.ts:242 🔧 Creating positioned tool shapes for 1 tool operations...
ocjsService.ts:249 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:806 Worker received message: createPositionedToolShapes
ocjsWorker.ts:220 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:316 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:848 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 20mm End Mill
ocjsService.ts:274 🔧 Performing sweep operations with 4 positioned tool shapes...
ocjsWorker.ts:806 Worker received message: performSweepOperation
ocjsWorker.ts:481 🔧 Starting sweep operation: subtract
ocjsWorker.ts:500 🔧 Processing 4 tool geometries
ocjsWorker.ts:503 🔍 Starting CSG operations on door body
ocjsWorker.ts:525 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:535 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:535 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:535 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:535 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:564 ✅ Sweep operation completed, result cached with ID: result_1751744021402
ocjsWorker.ts:848 Worker completed: performSweepOperation
ocjsService.ts:281 ✅ Sweep operations completed: result_1751744021402
ocjsService.ts:284 🔧 Exporting final result to GLB...
ocjsWorker.ts:806 Worker received message: exportGLB
ocjsWorker.ts:581 🔧 Exporting to GLB...
ocjsWorker.ts:606 🔍 Exporting final shape to GLB format
ocjsWorker.ts:631 ✅ GLB export completed, size: 4924 bytes
ocjsWorker.ts:848 Worker completed: exportGLB
ocjsService.ts:286 ✅ Final GLB exported, size: 4924 bytes
OCJSCanvas.vue:181 ✅ Worker processing completed, GLB data size: 4924
OCJSCanvas.vue:195 Model URL created: blob:http://localhost:1420/fc99984e-3546-486e-9515-9415a30d9f2e
OCJSCanvas.vue:233 ✅ Initializing Three.js scene
OCJSCanvas.vue:234 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:235 📐 Container dimensions: {width: 1200, height: 0, offsetWidth: 1200, offsetHeight: 0}
OCJSCanvas.vue:254 Container dimensions: {width: 1200, height: 600, aspect: 2}
OCJSCanvas.vue:270 Renderer created with size: 1200 x 600
OCJSCanvas.vue:271 Canvas element: <canvas data-engine=​"three.js r178" width=​"1200" height=​"600" style=​"display:​ block;​ width:​ 1200px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:272 Canvas style: display: block; width: 1200px; height: 600px;
OCJSCanvas.vue:307 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:319 🔄 Loading GLB model: blob:http://localhost:1420/fc99984e-3546-486e-9515-9415a30d9f2e
OCJSCanvas.vue:387 Loading progress: 100%
OCJSCanvas.vue:326 GLB model loaded successfully
OCJSCanvas.vue:350 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:362 Max dimension: 0.4
OCJSCanvas.vue:366 Camera distance: 1.2000000000000002
OCJSCanvas.vue:378 Model centered and camera adjusted
OCJSCanvas.vue:379 Camera position: _Vector3 {x: 1.2000000000000002, y: 0.9000000000000002, z: 1.2000000000000004}
OCJSCanvas.vue:380 Model position: _Vector3 {x: 0, y: 0, z: 0}
OCJSCanvas.vue:687 OCJSCanvas unmounted
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1200 x 660
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1200 x 660
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(0)}
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: {width: 300, height: 400, thickness: 18, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:154 Door parameters: {width: 0.3, height: 0.4, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:155 Door size in mm: W=300 H=400 T=18
OCJSCanvas.vue:172 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(0)}
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: {width: 0.3, height: 0.4, thickness: 0.018, cornerRadius: undefined, offsetX: 40, …}
ocjsWorker.ts:806 Worker received message: createDoorBody
ocjsWorker.ts:89 Creating box with dimensions: 0.3 0.4 0.018
ocjsWorker.ts:93 🚪 Door body dimensions: W=0.3m, H=0.4m, T=0.018m
ocjsWorker.ts:100 🚪 Door body centered at origin: X=[-0.15, 0.15], Y=[-0.2, 0.2], Z=[-0.009, 0.009]
ocjsWorker.ts:105 ✅ Door body cached with ID: door_1751744027985
ocjsWorker.ts:848 Worker completed: createDoorBody
OCJSCanvas.vue:592 OCJSCanvas mounted
ocjsService.ts:232 ✅ Door body created successfully
ocjsService.ts:239 🔍 All tool operations: ['20mm End Mill (cylindrical, ⌀20mm) - 4 commands']
ocjsService.ts:242 🔧 Creating positioned tool shapes for 1 tool operations...
ocjsService.ts:249 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:806 Worker received message: createPositionedToolShapes
ocjsWorker.ts:220 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:316 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:848 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 20mm End Mill
ocjsService.ts:274 🔧 Performing sweep operations with 4 positioned tool shapes...
ocjsWorker.ts:806 Worker received message: performSweepOperation
ocjsWorker.ts:481 🔧 Starting sweep operation: subtract
ocjsWorker.ts:500 🔧 Processing 4 tool geometries
ocjsWorker.ts:503 🔍 Starting CSG operations on door body
ocjsWorker.ts:525 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:535 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:535 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:535 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:535 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:564 ✅ Sweep operation completed, result cached with ID: result_1751744028138
ocjsWorker.ts:848 Worker completed: performSweepOperation
ocjsService.ts:281 ✅ Sweep operations completed: result_1751744028138
ocjsService.ts:284 🔧 Exporting final result to GLB...
ocjsWorker.ts:806 Worker received message: exportGLB
ocjsWorker.ts:581 🔧 Exporting to GLB...
ocjsWorker.ts:606 🔍 Exporting final shape to GLB format
ocjsWorker.ts:631 ✅ GLB export completed, size: 4924 bytes
ocjsWorker.ts:848 Worker completed: exportGLB
ocjsService.ts:286 ✅ Final GLB exported, size: 4924 bytes
OCJSCanvas.vue:181 ✅ Worker processing completed, GLB data size: 4924
OCJSCanvas.vue:195 Model URL created: blob:http://localhost:1420/954e95b2-d766-4690-a18e-d630e90e5617
OCJSCanvas.vue:233 ✅ Initializing Three.js scene
OCJSCanvas.vue:234 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:235 📐 Container dimensions: {width: 1200, height: 0, offsetWidth: 1200, offsetHeight: 0}
OCJSCanvas.vue:254 Container dimensions: {width: 1200, height: 600, aspect: 2}
OCJSCanvas.vue:270 Renderer created with size: 1200 x 600
OCJSCanvas.vue:271 Canvas element: <canvas data-engine=​"three.js r178" width=​"1200" height=​"600" style=​"display:​ block;​ width:​ 1200px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:272 Canvas style: display: block; width: 1200px; height: 600px;
OCJSCanvas.vue:307 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:319 🔄 Loading GLB model: blob:http://localhost:1420/954e95b2-d766-4690-a18e-d630e90e5617
OCJSCanvas.vue:387 Loading progress: 100%
OCJSCanvas.vue:326 GLB model loaded successfully
OCJSCanvas.vue:350 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:362 Max dimension: 0.4
OCJSCanvas.vue:366 Camera distance: 1.2000000000000002
OCJSCanvas.vue:378 Model centered and camera adjusted
OCJSCanvas.vue:379 Camera position: _Vector3 {x: 1.2000000000000002, y: 0.9000000000000002, z: 1.2000000000000004}
OCJSCanvas.vue:380 Model position: _Vector3 {x: 0, y: 0, z: 0}
