luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
VisualizationPanel.vue:1120 Converted makerjs JSON to 79 draw commands
EditorGroup.vue:102 Active file in group: group-1751668704634-6eh2ql06l File: script.lua Content length: 2787
OCJSCanvas.vue:682 OCJSCanvas unmounted
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 301 x 845
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 301 x 845
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
Editor.vue:66 Initializing Monaco Editor with content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThick...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
Editor.vue:259 Container dimensions: {width: 1568, height: 539}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: [Array(1)]
Editor.vue:299 Line 2 tokens: [Array(1)]
Editor.vue:299 Line 3 tokens: [Array(0)]
Editor.vue:299 Line 4 tokens: [Array(1)]
Editor.vue:299 Line 5 tokens: [Array(4)]
luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
App.vue:690 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\n-13\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 000001338D4F12C0\nDEBUG: Engine is available, proceeding with engine calls\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 01:42:41\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"H_Freze20mm_Ic_SF\": {\n      \"paths\": {\n        \"rect_bottom_41\": {\n          \"end\": [\n            676.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_44\": {\n          \"end\": [\n            676.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_43\": {\n          \"end\": [\n            1096.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_42\": {\n          \"end\": [\n            1096.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"H_Freze5mm_Ic\": {\n      \"paths\": {\n        \"polyline_seg_10\": {\n          \"end\": [\n            290.0,\n            238.174\n          ],\n          \"origin\": [\n            460.348,\n            153.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_11\": {\n          \"end\": [\n            119.652,\n            153.0\n          ],\n          \"origin\": [\n            290.0,\n            238.174\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_12\": {\n          \"end\": [\n            274.348,\n            246.0\n          ],\n          \"origin\": [\n            97.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_13\": {\n          \"end\": [\n            97.0,\n            334.674\n          ],\n          \"origin\": [\n            274.348,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_14\": {\n          \"end\": [\n            97.0,\n            157.326\n          ],\n          \"origin\": [\n            97.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_15\": {\n          \"end\": [\n            483.0,\n            157.326\n          ],\n          \"origin\": [\n            305.652,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_16\": {\n          \"end\": [\n            483.0,\n            334.674\n          ],\n          \"origin\": [\n            483.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_17\": {\n          \"end\": [\n            305.652,\n            246.0\n          ],\n          \"origin\": [\n            483.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_18\": {\n          \"end\": [\n            474.348,\n            346.0\n          ],\n          \"origin\": [\n            290.0,\n            253.826\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_19\": {\n          \"end\": [\n            290.0,\n            438.174\n          ],\n          \"origin\": [\n            474.348,\n            346.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_20\": {\n          \"end\": [\n            105.652,\n            346.0\n          ],\n          \
App.vue:694 Draw commands received: []
App.vue:696 MakerJS JSON received: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
VisualizationPanel.vue:1120 Converted makerjs JSON to 79 draw commands
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(2)}
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: undefined}
OCJSCanvas.vue:152 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
OCJSCanvas.vue:153 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:170 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(2)}
ocjsService.ts:221 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:223 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
OCJSCanvas.vue:587 OCJSCanvas mounted
ocjsWorker.ts:659 Worker received message: createDoorBody
ocjsWorker.ts:87 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:101 ✅ Door body cached with ID: door_1751668968405
ocjsWorker.ts:698 Worker completed: createDoorBody
ocjsService.ts:227 ✅ Door body created successfully
ocjsService.ts:231 🔍 All tools extracted: (3) ['6mm End Mill (cylindrical, ⌀6mm)', '20mm End Mill (cylindrical, ⌀20mm)', '20mm End Mill (cylindrical, ⌀20mm)']
ocjsService.ts:234 🔧 Creating BReps for 3 tools...
ocjsWorker.ts:659 Worker received message: createAllToolBReps
ocjsWorker.ts:309 Creating BReps for 3 tools...
ocjsWorker.ts:200 Creating BRep for cylindrical tool: 6mm End Mill (⌀6mm)
ocjsWorker.ts:277 ✅ Tool BRep cached with ID: tool_cyl-6mm_1751668968413
ocjsWorker.ts:315 ✓ Created BRep for 6mm End Mill
ocjsWorker.ts:200 Creating BRep for cylindrical tool: 20mm End Mill (⌀20mm)
ocjsWorker.ts:277 ✅ Tool BRep cached with ID: tool_cyl-20mm_1751668968413
ocjsWorker.ts:315 ✓ Created BRep for 20mm End Mill
ocjsWorker.ts:200 Creating BRep for cylindrical tool: 20mm End Mill (⌀20mm)
ocjsWorker.ts:277 ✅ Tool BRep cached with ID: tool_cyl-20mm_1751668968414
ocjsWorker.ts:315 ✓ Created BRep for 20mm End Mill
ocjsWorker.ts:698 Worker completed: createAllToolBReps
ocjsService.ts:240 ✅ Created 3/3 tool BReps
ocjsService.ts:241 🔍 Tool BRep results: (3) ['✅ 6mm End Mill', '✅ 20mm End Mill', '✅ 20mm End Mill']
ocjsService.ts:245 🔧 Performing sweep operations...
ocjsService.ts:247 🔍 Using tools for sweep: (3) ['tool_cyl-6mm_1751668968413', 'tool_cyl-20mm_1751668968413', 'tool_cyl-20mm_1751668968414']
ocjsWorker.ts:659 Worker received message: performSweepOperation
ocjsWorker.ts:344 🔧 Starting sweep operation: subtract
ocjsWorker.ts:363 🔧 Processing 3 tool geometries
ocjsWorker.ts:393 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:393 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:393 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:419 ✅ Sweep operation completed, result cached with ID: result_1751668968805
ocjsWorker.ts:698 Worker completed: performSweepOperation
ocjsService.ts:254 ✅ Sweep operations completed: result_1751668968805
ocjsService.ts:257 🔧 Exporting final result to GLB...
ocjsWorker.ts:659 Worker received message: exportGLB
ocjsWorker.ts:436 🔧 Exporting to GLB...
ocjsWorker.ts:484 ✅ GLB export completed, size: 3220 bytes
ocjsWorker.ts:698 Worker completed: exportGLB
ocjsService.ts:259 ✅ Final GLB exported, size: 3220 bytes
OCJSCanvas.vue:179 ✅ Worker processing completed, GLB data size: 3220
OCJSCanvas.vue:193 Model URL created: blob:http://localhost:1420/cef80fac-34b1-40d5-82ef-6567759ad06e
OCJSCanvas.vue:593 🔧 Force initializing Three.js on mount
OCJSCanvas.vue:231 ✅ Initializing Three.js scene
OCJSCanvas.vue:232 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:233 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
OCJSCanvas.vue:252 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:268 Renderer created with size: 1920 x 600
OCJSCanvas.vue:269 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:270 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:300 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:596 ✅ Three.js initialized on mount
OCJSCanvas.vue:231 ✅ Initializing Three.js scene
OCJSCanvas.vue:232 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:233 📐 Container dimensions: {width: 1920, height: 600, offsetWidth: 1920, offsetHeight: 600}
OCJSCanvas.vue:252 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:268 Renderer created with size: 1920 x 600
OCJSCanvas.vue:269 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:270 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:300 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:312 🔄 Loading GLB model: blob:http://localhost:1420/cef80fac-34b1-40d5-82ef-6567759ad06e
OCJSCanvas.vue:382 Loading progress: 100%
OCJSCanvas.vue:319 GLB model loaded successfully
OCJSCanvas.vue:343 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:355 Max dimension: 0.7
OCJSCanvas.vue:359 Camera distance: 2.0999999999999996
OCJSCanvas.vue:373 Model centered and camera adjusted
OCJSCanvas.vue:374 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
OCJSCanvas.vue:375 Model position: _Vector3 {x: 0, y: 0, z: 0.0045}
