colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Dark (vibrant-dark)
colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Light (vibrant-light)
colorfulThemeService.ts:190 ✅ Registered theme: Neon Dark (neon-dark)
ocjsService.ts:74 OC.js Worker initialized
colorfulThemeService.ts:236 Colorful themes initialized: (3) ['vibrant-dark', 'vibrant-light', 'neon-dark']
App.vue:1043 Adek<PERSON> Lua Editörü başlatıldı
App.vue:1057 Created welcome file: Untitled
EditorGroup.vue:102 Active file in group: group-1751742632330-qmk4k4dq2 File: Untitled Content length: 48
Editor.vue:66 Initializing Monaco Editor with content: -- <PERSON><PERSON>
-- Kodu<PERSON>zu buraya ekleyin

...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- <PERSON><PERSON>a dos<PERSON>ı
-- Kodu<PERSON>zu buraya ekleyin


Editor.vue:259 Container dimensions: {width: 1568, height: 839}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: [Array(1)]
Editor.vue:299 Line 2 tokens: [Array(1)]
Editor.vue:299 Line 3 tokens: [Array(0)]
Editor.vue:299 Line 4 tokens: [Array(0)]
useEditorState.ts:120 Creating new file: script.lua with content length: 2787
useEditorState.ts:121 Content preview: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThick...
EditorGroup.vue:102 Active file in group: group-1751742632330-qmk4k4dq2 File: script.lua Content length: 2787
Editor.vue:66 Initializing Monaco Editor with content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThick...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
Editor.vue:259 Container dimensions: {width: 1568, height: 839}
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
 Line 1 tokens: [Array(1)]
 Line 2 tokens: [Array(1)]
 Line 3 tokens: [Array(0)]
 Line 4 tokens: [Array(1)]
 Line 5 tokens: [Array(4)]
 === SENDING TO RUST ===
 Script content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
 Lua library path: ./LIBRARY\luaLibrary
 Debug mode: false
 === END SENDING TO RUST ===
 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\n-13\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012E5B335D60\nDEBUG: Engine is available, proceeding with engine calls\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 22:10:49\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"H_Freze20mm_Ic_SF\": {\n      \"paths\": {\n        \"rect_bottom_41\": {\n          \"end\": [\n            676.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_44\": {\n          \"end\": [\n            676.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_43\": {\n          \"end\": [\n            1096.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_42\": {\n          \"end\": [\n            1096.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"H_Freze5mm_Ic\": {\n      \"paths\": {\n        \"polyline_seg_10\": {\n          \"end\": [\n            290.0,\n            238.174\n          ],\n          \"origin\": [\n            460.348,\n            153.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_11\": {\n          \"end\": [\n            119.652,\n            153.0\n          ],\n          \"origin\": [\n            290.0,\n            238.174\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_12\": {\n          \"end\": [\n            274.348,\n            246.0\n          ],\n          \"origin\": [\n            97.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_13\": {\n          \"end\": [\n            97.0,\n            334.674\n          ],\n          \"origin\": [\n            274.348,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_14\": {\n          \"end\": [\n            97.0,\n            157.326\n          ],\n          \"origin\": [\n            97.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_15\": {\n          \"end\": [\n            483.0,\n            157.326\n          ],\n          \"origin\": [\n            305.652,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_16\": {\n          \"end\": [\n            483.0,\n            334.674\n          ],\n          \"origin\": [\n            483.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_17\": {\n          \"end\": [\n            305.652,\n            246.0\n          ],\n          \"origin\": [\n            483.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_18\": {\n          \"end\": [\n            474.348,\n            346.0\n          ],\n          \"origin\": [\n            290.0,\n            253.826\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_19\": {\n          \"end\": [\n            290.0,\n            438.174\n          ],\n          \"origin\": [\n            474.348,\n            346.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_20\": {\n          \"end\": [\n            105.652,\n            346.0\n          ],\n          \
 Draw commands received: []
 MakerJS JSON received: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
 Processing makerjs JSON: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
 Converted makerjs JSON to 79 draw commands
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
 Canvas size set to: 1920 x 877
 Drawing 59 commands
 drawCanvas completed
 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: {panelCommands: Array(4), topTools: Array(1), bottomTools: Array(2)}
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: undefined, offsetX: 40, …}
OCJSCanvas.vue:152 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
OCJSCanvas.vue:153 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:170 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(1), bottomTools: Array(2)}
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: undefined}
OCJSCanvas.vue:590 OCJSCanvas mounted
ocjsWorker.ts:806 Worker received message: createDoorBody
 OpenCascade.js initialized in worker
 Creating box with dimensions: 0.5 0.7 0.018
 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
 ✅ Door body cached with ID: door_1751742669624
 Worker completed: createDoorBody
 ✅ Door body created successfully
 🔍 All tool operations: (3) ['6mm End Mill (cylindrical, ⌀6mm) - 32 commands', '20mm End Mill (cylindrical, ⌀20mm) - 4 commands', '20mm End Mill (cylindrical, ⌀20mm) - 19 commands']
 🔧 Creating positioned tool shapes for 3 tool operations...
 🔧 Processing 6mm End Mill with 32 commands
 Worker received message: createPositionedToolShapes
 🔧 Creating 32 positioned cylindrical tool shapes for 6mm End Mill
 🔧 Positioning line tool 0 at: X=0.1252m, Y=0.0015m, Z=-0.1544m (from 375.174, 195.587 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 1 at: X=-0.0452m, Y=0.0015m, Z=-0.1544m (from 204.826, 195.587 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 2 at: X=-0.0643m, Y=0.0015m, Z=-0.1483m (from 185.674, 201.663 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 3 at: X=-0.0643m, Y=0.0015m, Z=-0.0597m (from 185.674, 290.337 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 4 at: X=-0.1530m, Y=0.0015m, Z=-0.1040m (from 97, 246 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 5 at: X=0.1443m, Y=0.0015m, Z=-0.1483m (from 394.326, 201.663 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 6 at: X=0.2330m, Y=0.0015m, Z=-0.1040m (from 483, 246 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 7 at: X=0.1443m, Y=0.0015m, Z=-0.0597m (from 394.326, 290.337 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 8 at: X=0.1322m, Y=0.0015m, Z=-0.0501m (from 382.174, 299.913 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 9 at: X=0.1322m, Y=0.0015m, Z=0.0421m (from 382.174, 392.087 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 10 at: X=-0.0522m, Y=0.0015m, Z=0.0421m (from 197.826, 392.087 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 11 at: X=-0.0522m, Y=0.0015m, Z=-0.0501m (from 197.826, 299.913 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 12 at: X=0.1443m, Y=0.0015m, Z=0.0517m (from 394.326, 401.663 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 13 at: X=0.2330m, Y=0.0015m, Z=0.0960m (from 483, 446 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 14 at: X=0.1443m, Y=0.0015m, Z=0.1403m (from 394.326, 490.337 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 15 at: X=-0.0452m, Y=0.0015m, Z=0.3464m (from 204.826, 696.413 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 16 at: X=0.1252m, Y=0.0015m, Z=0.3464m (from 375.174, 696.413 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 17 at: X=0.0400m, Y=0.0015m, Z=0.3890m (from 290, 739 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 18 at: X=-0.1530m, Y=0.0015m, Z=0.2960m (from 97, 646 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 19 at: X=-0.0643m, Y=0.0015m, Z=0.2517m (from 185.674, 601.663 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 20 at: X=-0.0643m, Y=0.0015m, Z=0.3403m (from 185.674, 690.337 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 21 at: X=0.1443m, Y=0.0015m, Z=0.2517m (from 394.326, 601.663 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 22 at: X=0.2330m, Y=0.0015m, Z=0.2960m (from 483, 646 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 23 at: X=0.1443m, Y=0.0015m, Z=0.3403m (from 394.326, 690.337 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 24 at: X=-0.0522m, Y=0.0015m, Z=0.1499m (from 197.826, 499.913 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 25 at: X=0.1322m, Y=0.0015m, Z=0.1499m (from 382.174, 499.913 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 26 at: X=0.1322m, Y=0.0015m, Z=0.2421m (from 382.174, 592.087 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 27 at: X=-0.0522m, Y=0.0015m, Z=0.2421m (from 197.826, 592.087 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 28 at: X=-0.0643m, Y=0.0015m, Z=0.1403m (from 185.674, 490.337 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 29 at: X=-0.1530m, Y=0.0015m, Z=0.0960m (from 97, 446 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 30 at: X=-0.0643m, Y=0.0015m, Z=0.0517m (from 185.674, 401.663 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 31 at: X=0.0400m, Y=0.0015m, Z=-0.1970m (from 290, 153 mm, offset: 0, 0, door: 500x700 mm)
 ✅ Created 32 positioned tool shapes
 Worker completed: createPositionedToolShapes
 ✅ Created 32 positioned shapes for 6mm End Mill
 🔧 Processing 20mm End Mill with 4 commands
 Worker received message: createPositionedToolShapes
 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
 🔧 Positioning line tool 0 at: X=0.6360m, Y=-0.0050m, Z=0.4060m (from 886, 756 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 1 at: X=0.4260m, Y=-0.0050m, Z=0.0960m (from 676, 446 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 2 at: X=0.8460m, Y=-0.0050m, Z=0.0960m (from 1096, 446 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 3 at: X=0.6360m, Y=-0.0050m, Z=-0.2140m (from 886, 136 mm, offset: 0, 0, door: 500x700 mm)
 ✅ Created 4 positioned tool shapes
 Worker completed: createPositionedToolShapes
 ✅ Created 4 positioned shapes for 20mm End Mill
 🔧 Processing 20mm End Mill with 19 commands
 Worker received message: createPositionedToolShapes
 🔧 Creating 19 positioned cylindrical tool shapes for 20mm End Mill
 🔧 Positioning line tool 0 at: X=0.6360m, Y=-0.0050m, Z=-0.1040m (from 886, 246 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 1 at: X=0.6360m, Y=-0.0050m, Z=-0.1040m (from 886, 246 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 2 at: X=0.6360m, Y=-0.0050m, Z=0.0960m (from 886, 446 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 3 at: X=0.6360m, Y=-0.0050m, Z=0.0960m (from 886, 446 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 4 at: X=0.6360m, Y=-0.0050m, Z=0.2960m (from 886, 646 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 5 at: X=0.6360m, Y=-0.0050m, Z=0.2960m (from 886, 646 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 6 at: X=-0.2452m, Y=-0.0050m, Z=-0.2013m (from 4.8295, 148.70600000000002 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 7 at: X=0.0013m, Y=-0.0050m, Z=0.2978m (from 251.29399999999998, 647.8295 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 8 at: X=-0.2452m, Y=-0.0050m, Z=-0.2513m (from 4.8295, 98.706 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 9 at: X=-0.1978m, Y=-0.0050m, Z=-0.1987m (from 52.170500000000004, 151.29399999999998 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 10 at: X=-0.0013m, Y=-0.0050m, Z=0.3452m (from 248.70600000000002, 695.1705 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 11 at: X=-0.2148m, Y=-0.0050m, Z=-0.2487m (from 35.170500000000004, 101.294 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 12 at: X=-0.0037m, Y=-0.0050m, Z=-0.1889m (from 246.25900000000001, 161.14350000000002 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 13 at: X=0.0194m, Y=-0.0050m, Z=-0.2033m (from 269.3935, 146.6825 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 14 at: X=-0.0089m, Y=-0.0050m, Z=-0.1877m (from 241.0555, 162.2985 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 15 at: X=0.0246m, Y=-0.0050m, Z=-0.2045m (from 274.5965, 145.52800000000002 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 16 at: X=-0.2215m, Y=-0.0050m, Z=-0.2000m (from 28.5, 150 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 17 at: X=0.0000m, Y=-0.0050m, Z=0.3215m (from 250, 671.5 mm, offset: 0, 0, door: 500x700 mm)
 🔧 Positioning line tool 18 at: X=-0.2300m, Y=-0.0050m, Z=-0.2500m (from 20, 100 mm, offset: 0, 0, door: 500x700 mm)
 ✅ Created 19 positioned tool shapes
 Worker completed: createPositionedToolShapes
 ✅ Created 19 positioned shapes for 20mm End Mill
 🔧 Performing sweep operations with 55 positioned tool shapes...
 Worker received message: performSweepOperation
 🔧 Starting sweep operation: subtract
 🔧 Processing 55 tool geometries
 🔍 Starting CSG operations on door body
 🔧 Attempting to subtract tool 0...
 ✅ Tool 0 subtracted successfully
 🔧 Attempting to subtract tool 1...
 ✅ Tool 1 subtracted successfully
 🔧 Attempting to subtract tool 2...
 ✅ Tool 2 subtracted successfully
 🔧 Attempting to subtract tool 3...
 ✅ Tool 3 subtracted successfully
 🔧 Attempting to subtract tool 4...
 ✅ Tool 4 subtracted successfully
 🔧 Attempting to subtract tool 5...
 ✅ Tool 5 subtracted successfully
 🔧 Attempting to subtract tool 6...
 ✅ Tool 6 subtracted successfully
 🔧 Attempting to subtract tool 7...
 ✅ Tool 7 subtracted successfully
 🔧 Attempting to subtract tool 8...
 ✅ Tool 8 subtracted successfully
 🔧 Attempting to subtract tool 9...
 ✅ Tool 9 subtracted successfully
 🔧 Attempting to subtract tool 10...
 ✅ Tool 10 subtracted successfully
 🔧 Attempting to subtract tool 11...
 ✅ Tool 11 subtracted successfully
 🔧 Attempting to subtract tool 12...
 ✅ Tool 12 subtracted successfully
 🔧 Attempting to subtract tool 13...
 ✅ Tool 13 subtracted successfully
 🔧 Attempting to subtract tool 14...
 ✅ Tool 14 subtracted successfully
 🔧 Attempting to subtract tool 15...
 ✅ Tool 15 subtracted successfully
 🔧 Attempting to subtract tool 16...
 ✅ Tool 16 subtracted successfully
 🔧 Attempting to subtract tool 17...
 ✅ Tool 17 subtracted successfully
 🔧 Attempting to subtract tool 18...
 ✅ Tool 18 subtracted successfully
 🔧 Attempting to subtract tool 19...
 ✅ Tool 19 subtracted successfully
 🔧 Attempting to subtract tool 20...
 ✅ Tool 20 subtracted successfully
 🔧 Attempting to subtract tool 21...
 ✅ Tool 21 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 22...
ocjsWorker.ts:535 ✅ Tool 22 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 23...
ocjsWorker.ts:535 ✅ Tool 23 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 24...
ocjsWorker.ts:535 ✅ Tool 24 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 25...
ocjsWorker.ts:535 ✅ Tool 25 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 26...
ocjsWorker.ts:535 ✅ Tool 26 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 27...
ocjsWorker.ts:535 ✅ Tool 27 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 28...
ocjsWorker.ts:535 ✅ Tool 28 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 29...
ocjsWorker.ts:535 ✅ Tool 29 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 30...
ocjsWorker.ts:535 ✅ Tool 30 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 31...
ocjsWorker.ts:535 ✅ Tool 31 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 32...
ocjsWorker.ts:535 ✅ Tool 32 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 33...
ocjsWorker.ts:535 ✅ Tool 33 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 34...
ocjsWorker.ts:535 ✅ Tool 34 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 35...
ocjsWorker.ts:535 ✅ Tool 35 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 36...
ocjsWorker.ts:535 ✅ Tool 36 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 37...
ocjsWorker.ts:535 ✅ Tool 37 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 38...
ocjsWorker.ts:535 ✅ Tool 38 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 39...
ocjsWorker.ts:535 ✅ Tool 39 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 40...
ocjsWorker.ts:535 ✅ Tool 40 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 41...
ocjsWorker.ts:535 ✅ Tool 41 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 42...
ocjsWorker.ts:535 ✅ Tool 42 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 43...
ocjsWorker.ts:535 ✅ Tool 43 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 44...
ocjsWorker.ts:535 ✅ Tool 44 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 45...
ocjsWorker.ts:535 ✅ Tool 45 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 46...
ocjsWorker.ts:535 ✅ Tool 46 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 47...
ocjsWorker.ts:535 ✅ Tool 47 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 48...
ocjsWorker.ts:535 ✅ Tool 48 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 49...
ocjsWorker.ts:535 ✅ Tool 49 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 50...
ocjsWorker.ts:535 ✅ Tool 50 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 51...
ocjsWorker.ts:535 ✅ Tool 51 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 52...
ocjsWorker.ts:535 ✅ Tool 52 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 53...
ocjsWorker.ts:535 ✅ Tool 53 subtracted successfully
ocjsWorker.ts:525 🔧 Attempting to subtract tool 54...
ocjsWorker.ts:535 ✅ Tool 54 subtracted successfully
ocjsWorker.ts:564 ✅ Sweep operation completed, result cached with ID: result_1751742671937
ocjsWorker.ts:848 Worker completed: performSweepOperation
ocjsService.ts:281 ✅ Sweep operations completed: result_1751742671937
ocjsService.ts:284 🔧 Exporting final result to GLB...
ocjsWorker.ts:806 Worker received message: exportGLB
ocjsWorker.ts:581 🔧 Exporting to GLB...
ocjsWorker.ts:606 🔍 Exporting final shape to GLB format
ocjsWorker.ts:631 ✅ GLB export completed, size: 3232 bytes
ocjsWorker.ts:848 Worker completed: exportGLB
ocjsService.ts:286 ✅ Final GLB exported, size: 3232 bytes
OCJSCanvas.vue:179 ✅ Worker processing completed, GLB data size: 3232
OCJSCanvas.vue:193 Model URL created: blob:http://localhost:1420/c2adaf45-59db-42b9-9ba6-e7fcb62e84df
OCJSCanvas.vue:231 ✅ Initializing Three.js scene
OCJSCanvas.vue:232 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:233 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
OCJSCanvas.vue:252 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:268 Renderer created with size: 1920 x 600
OCJSCanvas.vue:269 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:270 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:305 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:317 🔄 Loading GLB model: blob:http://localhost:1420/c2adaf45-59db-42b9-9ba6-e7fcb62e84df
OCJSCanvas.vue:385 Loading progress: 100%
OCJSCanvas.vue:324 GLB model loaded successfully
OCJSCanvas.vue:348 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:360 Max dimension: 0.7
OCJSCanvas.vue:364 Camera distance: 2.0999999999999996
OCJSCanvas.vue:376 Model centered and camera adjusted
OCJSCanvas.vue:377 Camera position: _Vector3 {x: 2.0999999999999996, y: 1.575, z: 2.1}
OCJSCanvas.vue:378 Model position: _Vector3 {x: 0, y: 0, z: 0}
