// OpenCascade.js Service - Fixed GLB generation - FORCE RELOAD: 2025-01-04-12:05
import type {
  OCJSWorkerMessage,
  OCJSWorkerResponse,
  DoorBodyParams,
  ToolGeometryParams,
  SweepOperationParams,
  ToolBRepParams,
  AllToolBRepsParams
} from '../workers/ocjsWorker'
import type { CNCTool, DrawCommand } from '../types'

export class OCJSService {
  private static instance: OCJSService
  private worker: Worker | null = null
  private messageId = 0
  private pendingMessages = new Map<string, {
    resolve: (value: any) => void
    reject: (error: Error) => void
  }>()

  private constructor() {
    this.initializeWorker()
  }

  public static getInstance(): OCJSService {
    // FORCE RELOAD: Reset instance to ensure new code is used
    OCJSService.instance = new OCJSService()
    return OCJSService.instance
  }

  private initializeWorker() {
    try {
      // Create worker from TypeScript file
      this.worker = new Worker(
        new URL('../workers/ocjsWorker.ts?worker', import.meta.url),
        { type: 'module' }
      )

      this.worker.onmessage = (event: MessageEvent<OCJSWorkerResponse>) => {
        const { id, type, data, error } = event.data
        const pending = this.pendingMessages.get(id)

        if (pending) {
          this.pendingMessages.delete(id)

          if (type === 'success') {
            pending.resolve(data)
          } else {
            pending.reject(new Error(error || 'Unknown worker error'))
          }
        }
      }

      this.worker.onerror = (error) => {
        console.error('OC.js Worker error:', error)
        // Try to reinitialize worker after a delay
        setTimeout(() => {
          this.worker = null
          this.initializeWorker()
        }, 1000)

        // Reject all pending messages
        this.pendingMessages.forEach(({ reject }) => {
          reject(new Error('Worker error - reinitializing'))
        })
        this.pendingMessages.clear()
      }

      this.worker.onmessageerror = (error) => {
        console.error('OC.js Worker message error:', error)
      }

      console.log('OC.js Worker initialized')
    } catch (error) {
      console.error('Failed to initialize OC.js Worker:', error)
      // Retry initialization after delay
      setTimeout(() => {
        this.initializeWorker()
      }, 2000)
    }
  }

  private sendMessage<T>(type: OCJSWorkerMessage['type'], data: any): Promise<T> {
    return new Promise((resolve, reject) => {
      if (!this.worker) {
        reject(new Error('Worker not initialized'))
        return
      }

      const id = `msg_${++this.messageId}`
      this.pendingMessages.set(id, { resolve, reject })

      // Serialize data to ensure it can be cloned
      let serializedData: any
      try {
        serializedData = this.serializeData(data)
      } catch (error) {
        reject(new Error(`Failed to serialize data: ${error}`))
        return
      }

      const message: OCJSWorkerMessage = { id, type, data: serializedData }

      try {
        this.worker.postMessage(message)
      } catch (error) {
        this.pendingMessages.delete(id)
        reject(new Error(`Failed to send message to worker: ${error}`))
        return
      }

      // Set timeout for message
      setTimeout(() => {
        if (this.pendingMessages.has(id)) {
          this.pendingMessages.delete(id)
          reject(new Error('Worker message timeout'))
        }
      }, 30000) // 30 second timeout
    })
  }

  // Serialize data to ensure it can be cloned by postMessage
  private serializeData(data: any): any {
    if (data === null || data === undefined) {
      return data
    }

    if (typeof data === 'string' || typeof data === 'number' || typeof data === 'boolean') {
      return data
    }

    if (Array.isArray(data)) {
      return data.map(item => this.serializeData(item))
    }

    if (typeof data === 'object') {
      const serialized: any = {}
      for (const [key, value] of Object.entries(data)) {
        // Skip functions and non-serializable objects
        if (typeof value === 'function') {
          continue
        }

        // Skip circular references and complex objects
        if (value && typeof value === 'object') {
          // Check for common non-serializable objects
          if (value.constructor && value.constructor.name !== 'Object' && value.constructor.name !== 'Array') {
            // Skip Three.js objects, DOM elements, etc.
            continue
          }
        }

        serialized[key] = this.serializeData(value)
      }
      return serialized
    }

    return data
  }

  // Create door body from PANEL layer data
  public async createDoorBody(params: DoorBodyParams): Promise<any> {
    return this.sendMessage('createDoorBody', params)
  }

  // Create tool geometries from parsed tool data
  public async createToolGeometry(params: ToolGeometryParams): Promise<any[]> {
    return this.sendMessage('createToolGeometry', params)
  }

  // Perform sweep operation (boolean subtraction)
  public async performSweepOperation(params: SweepOperationParams): Promise<any> {
    return this.sendMessage('performSweepOperation', params)
  }

  // Export shape to GLB format
  public async exportToGLB(shape: any): Promise<ArrayBuffer> {
    return this.sendMessage('exportGLB', shape)
  }

  // Create a simple box GLB file for testing
  public async createSimpleBoxGLB(doorParams: DoorBodyParams): Promise<ArrayBuffer> {
    return this.sendMessage<ArrayBuffer>('createSimpleBoxGLB', doorParams)
  }

  // Create BRep for a single tool
  public async createToolBRep(params: ToolBRepParams): Promise<any> {
    return this.sendMessage('createToolBRep', params)
  }

  // Create BReps for all tools
  public async createAllToolBReps(params: AllToolBRepsParams): Promise<any> {
    return this.sendMessage('createAllToolBReps', params)
  }

  // Create positioned tool shapes for each command
  public async createPositionedToolShapes(params: any): Promise<any> {
    return this.sendMessage('createPositionedToolShapes', params)
  }

  // Test worker connectivity
  public async testWorker(): Promise<boolean> {
    try {
      // Try to create a simple door body to test the worker
      const testParams = {
        width: 100,
        height: 100,
        thickness: 18
      }
      await this.createDoorBody(testParams)
      console.log('OC.js Worker test successful')
      return true
    } catch (error) {
      console.error('OC.js Worker test failed:', error)
      return false
    }
  }

  // High-level method to process door with tools
  public async processDoorWithTools(
    doorParams: DoorBodyParams,
    topTools: { tool: CNCTool; commands: DrawCommand[]; depth: number }[],
    bottomTools: { tool: CNCTool; commands: DrawCommand[]; depth: number }[]
  ): Promise<ArrayBuffer> {
    console.log('🔧 SWEEP OPERATION: Starting door processing with tools')
    try {
      console.log('📐 Creating door body with params:', doorParams)

      // Step 1: Create door body
      const doorBodyResult = await this.createDoorBody(doorParams)
      console.log('✅ Door body created successfully')

      // Step 2: Create positioned tool shapes for each tool and command
      const allToolOperations = [
        ...topTools.map(t => ({ ...t, isBottomFace: false })),
        ...bottomTools.map(t => ({ ...t, isBottomFace: true }))
      ]
      console.log('🔍 All tool operations:', allToolOperations.map(t => `${t.tool.name} (${t.tool.shape}, ⌀${t.tool.diameter}mm) - ${t.commands.length} commands`))

      if (allToolOperations.length > 0) {
        console.log(`🔧 Creating positioned tool shapes for ${allToolOperations.length} tool operations...`)

        const allPositionedShapeIds: string[] = []

        // Create positioned tool shapes for each tool operation
        for (const toolOp of allToolOperations) {
          try {
            console.log(`🔧 Processing ${toolOp.tool.name} with ${toolOp.commands.length} commands`)
            const positionedResult = await this.sendMessage('createPositionedToolShapes', {
              tool: toolOp.tool,
              commands: toolOp.commands,
              depth: toolOp.depth,
              isBottomFace: toolOp.isBottomFace,
              doorWidth: doorParams.width,
              doorHeight: doorParams.height
            })

            if (positionedResult.success) {
              allPositionedShapeIds.push(...positionedResult.shapeIds)
              console.log(`✅ Created ${positionedResult.count} positioned shapes for ${toolOp.tool.name}`)
            } else {
              console.warn(`⚠️ Failed to create positioned shapes for ${toolOp.tool.name}`)
            }
          } catch (error) {
            console.error(`❌ Error creating positioned shapes for ${toolOp.tool.name}:`, error)
          }
        }

        // Step 3: Perform sweep operations (subtract positioned tools from door)
        if (allPositionedShapeIds.length > 0) {
          console.log(`🔧 Performing sweep operations with ${allPositionedShapeIds.length} positioned tool shapes...`)

          const sweepResult = await this.performSweepOperation({
            doorBodyShape: doorBodyResult.shapeId || doorBodyResult,
            toolGeometries: allPositionedShapeIds,
            operation: 'subtract'
          })
          console.log('✅ Sweep operations completed:', sweepResult.shapeId)

          // Step 4: Export final result to GLB
          console.log('🔧 Exporting final result to GLB...')
          const finalGLB = await this.exportToGLB(sweepResult.shapeId)
          console.log('✅ Final GLB exported, size:', finalGLB.byteLength, 'bytes')

          return finalGLB
        } else {
          console.warn('⚠️ No positioned tool shapes created, cannot perform sweep operations')
        }
      } else {
        console.warn('⚠️ No tool operations found for processing')
      }

      // Fallback: If no tools or sweep operations failed, return simple door body
      console.log('⚠️ No tools processed, returning simple door body GLB')
      const simpleGLB = await this.createSimpleBoxGLB(doorParams)
      console.log('✅ Simple door GLB created, size:', simpleGLB.byteLength, 'bytes')

      return simpleGLB
    } catch (error) {
      console.error('❌ Error processing door with tools:', error)

      // Fallback on error: return simple door body
      console.log('🔄 Falling back to simple door body due to error')
      try {
        const fallbackGLB = await this.createSimpleBoxGLB(doorParams)
        console.log('✅ Fallback GLB created, size:', fallbackGLB.byteLength, 'bytes')
        return fallbackGLB
      } catch (fallbackError) {
        console.error('❌ Fallback also failed:', fallbackError)
        throw error
      }
    }
  }

  // Extract door parameters from PANEL layer commands
  public extractDoorParameters(panelCommands: DrawCommand[]): DoorBodyParams {
    if (!panelCommands || panelCommands.length === 0) {
      // Default door parameters
      return {
        width: 600,
        height: 400,
        thickness: 18
      }
    }

    // Find bounding box of PANEL commands
    let minX = Infinity, maxX = -Infinity
    let minY = Infinity, maxY = -Infinity
    let thickness = 18 // Default thickness
    let hasArcs = false

    panelCommands.forEach(cmd => {
      if (cmd.command_type === 'line') {
        minX = Math.min(minX, cmd.x1, cmd.x2)
        maxX = Math.max(maxX, cmd.x1, cmd.x2)
        minY = Math.min(minY, cmd.y1, cmd.y2)
        maxY = Math.max(maxY, cmd.y1, cmd.y2)
      } else if (cmd.command_type === 'circle') {
        const radius = cmd.radius || 0
        // For circles, use x1,y1 as center coordinates
        minX = Math.min(minX, cmd.x1 - radius)
        maxX = Math.max(maxX, cmd.x1 + radius)
        minY = Math.min(minY, cmd.y1 - radius)
        maxY = Math.max(maxY, cmd.y1 + radius)
      } else if (cmd.command_type === 'arc') {
        hasArcs = true
        // For arcs, use x1,y1 as center and radius to estimate bounds
        const radius = cmd.radius || 0
        minX = Math.min(minX, cmd.x1 - radius)
        maxX = Math.max(maxX, cmd.x1 + radius)
        minY = Math.min(minY, cmd.y1 - radius)
        maxY = Math.max(maxY, cmd.y1 + radius)
      }

      // Extract thickness from command if available
      if (cmd.thickness && Math.abs(cmd.thickness) > 0) {
        thickness = Math.abs(cmd.thickness)
      }
    })

    const width = maxX - minX
    const height = maxY - minY

    // Estimate corner radius if arcs are present
    let cornerRadius = 0
    if (hasArcs) {
      // Find the smallest arc radius as corner radius estimate
      const arcCommands = panelCommands.filter(cmd => cmd.command_type === 'arc')
      if (arcCommands.length > 0) {
        cornerRadius = Math.min(...arcCommands.map(cmd => cmd.radius || 0))
      }
    }

    return {
      width: width > 0 ? width : 600,
      height: height > 0 ? height : 400,
      thickness,
      cornerRadius: cornerRadius > 0 ? cornerRadius : undefined
    }
  }

  // Clean up worker
  public dispose() {
    if (this.worker) {
      this.worker.terminate()
      this.worker = null
    }
    this.pendingMessages.clear()
  }
}

// Export singleton instance - FORCE RELOAD v2.1
export const ocjsService = OCJSService.getInstance()
