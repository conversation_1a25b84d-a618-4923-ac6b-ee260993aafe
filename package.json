{"name": "lua-macro-editor", "version": "1.0.11", "description": "Lua Macro Editor for Adeko Libraries", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "setup-lua": "node scripts/setup-lua.cjs", "prebuild": "npm run setup-lua", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "npm run setup-lua && tauri build", "version": "node scripts/version.js", "version:patch": "node scripts/increment-version.js patch", "version:minor": "node scripts/increment-version.js minor", "version:major": "node scripts/increment-version.js major"}, "dependencies": {"@tauri-apps/plugin-dialog": "^2.0.0", "@vueuse/core": "^10.7.0", "lucide-vue-next": "^0.294.0", "monaco-editor": "^0.45.0", "opencascade.js": "^2.0.0-beta.c301f5e", "three": "^0.178.0", "vue": "^3.4.0", "vue-i18n": "^9.14.4"}, "devDependencies": {"@tauri-apps/api": "^2.0.0", "@tauri-apps/cli": "^2.0.0", "@types/node": "^20.10.0", "@types/three": "^0.178.0", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.0", "vite": "^6.3.5", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-top-level-await": "^1.5.0", "vite-plugin-wasm": "^3.4.1", "vue-tsc": "^2.2.10"}}